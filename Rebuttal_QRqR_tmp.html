<!DOCTYPE html>
<html>
<head>
<title>Rebuttal_QRqR.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>
<link rel="stylesheet" href="file:///c%3A/Users/<USER>/.markdown-css/misty-light-windows.css" type="text/css">
<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="rebuttal-to-reviewer-qrqr-score-3-con-4">Rebuttal to Reviewer QRqR (Score 3; Con 4)</h1>
<p>We sincerely thank Reviewer QRqR for the constructive feedback. We address each concern below with detailed responses and clarifications.</p>
<h2 id="response-to-limitation-1-expert-review-and-post-translation-validation-details">Response to Limitation 1: Expert Review and Post-Translation Validation Details</h2>
<p>We acknowledge the reviewer's concern regarding the lack of specific details on expert review and post-translation validation, including the number of reviewers, validation accuracy rates, and validation criteria. We provide the following comprehensive information about our expert review process.</p>
<p>Our validation process employed two expert anesthesiologists, each with over 10 years of clinical experience, to conduct thorough quality assessment. We randomly selected 500 questions from AnesBench for expert review, representing approximately 11.3% of the total dataset. This sample size was chosen to provide statistically meaningful insights while maintaining feasibility given the specialized expertise required.</p>
<p>The expert review focused on three critical aspects of question quality. First, we evaluated professional accuracy, examining the correctness of medical concepts and clinical scenarios presented in each question. Second, we assessed question quality, including clarity, relevance, and appropriateness of difficulty level for the intended evaluation purpose. Third, we scrutinized translation accuracy, ensuring precision of medical terminology translation and preservation of clinical meaning across languages.</p>
<p>Our methodology employed a cross-validation approach to maximize reliability. Each expert independently reviewed all 500 questions to minimize bias and ensure comprehensive coverage. For questions where one expert expressed uncertainty, both experts collaborated to reach consensus through detailed discussion. Any disagreements were systematically resolved through reference to authoritative anesthesiology guidelines and established clinical standards.</p>
<p>The validation results demonstrated high overall quality across our benchmark. Our expert review revealed that only 1 question (0.2%) contained significant professional inaccuracies that could affect clinical understanding, representing an exceptionally low rate of serious errors. Additionally, 23 questions (4.6%) had minor translation issues that did not compromise professional accuracy, while 18 questions (3.6%) presented minor formatting or clarity issues that could be easily addressed. Most importantly, 458 questions (91.6%) were deemed professionally accurate and appropriately translated, meeting all validation criteria without requiring modifications.</p>
<p>The overall validation accuracy rate of 91.6% demonstrates the high quality of our benchmark construction process. These detailed validation results will be incorporated into Appendix J in the revised version of our paper to provide complete transparency regarding our quality assurance procedures.</p>
<h2 id="response-to-limitation-2-quality-assurance-of-llm-generated-qa-data">Response to Limitation 2: Quality Assurance of LLM-Generated QA Data</h2>
<p>We appreciate the reviewer's important question regarding how to ensure the informativeness of questions and the effectiveness/accuracy of answers generated by LLaMA-70B and Qwen-72B-Instruct. We provide the following comprehensive clarifications to address this concern.</p>
<p>We first emphasize that QA data generation is not the primary contribution of our work. Our main contribution is the comprehensive benchmark AnesBench and the systematic evaluation of LLMs' anesthesiology reasoning capabilities. The QA data (AnesQA) was generated specifically to validate whether domain-specific training data can improve model reasoning performance, which our experiments successfully demonstrated. This distinction is important for understanding the scope and purpose of our synthetic data generation efforts.</p>
<p>Despite QA data not being our primary focus, we implemented several robust quality assurance mechanisms to ensure data reliability. We employed a multi-model cross-validation approach, utilizing two different state-of-the-art models (LLaMA3.3-70B-Instruct for question generation and Qwen2.5-72B-Instruct for answer generation and filtering) to reduce single-model bias and improve overall quality. Additionally, questions were generated from carefully filtered text segments using domain-specific keywords, ensuring topical relevance and informativeness. Our constrained generation approach explicitly required that answers be direct rephrasings of the source text segments, minimizing hallucination and ensuring factual grounding in the original medical literature.</p>
<p>The effectiveness of our approach is supported by empirical validation. As demonstrated in Table 6 (Training Strategies), models trained on AnesQA showed consistent performance improvements, validating the effectiveness of our synthetic data for its intended purpose. Furthermore, this approach aligns with established practices in the field, as synthetic SFT data containing some noise can still improve model performance, supported by recent research. This methodology represents a common practice in specialized domains due to the prohibitive cost of manual annotation for highly technical medical content.</p>
<h2 id="response-to-limitation-3-manual-quality-assessment-of-synthetic-data">Response to Limitation 3: Manual Quality Assessment of Synthetic Data</h2>
<p>We understand the reviewer's concern regarding the synthetic data lacking detailed descriptions of manual quality assessment and screening processes. We provide the following comprehensive response addressing this limitation.</p>
<p>It is important to contextualize our approach within current industry practices. Full manual review of large-scale QA datasets is not standard practice in the field due to prohibitive costs and resource requirements. Most recent works in medical AI rely on automated quality assurance mechanisms combined with sampling-based validation, which represents the current state-of-the-art approach. Given the specialized nature of anesthesiology, comprehensive manual annotation would require extensive expert time from qualified anesthesiologists, making it impractical for large-scale dataset creation while maintaining reasonable research timelines and resource constraints.</p>
<p>Despite these constraints, we conducted a thorough sampling-based manual quality assessment to ensure data reliability. We randomly selected 100 QA pairs from AnesQA for expert review, providing a representative sample for quality evaluation. The expert assessment revealed several important findings regarding data quality. Experts found questions to be specific, focused, and informative, meeting the basic requirements for training data. Responses contained minimal factual errors and were generally accurate in their medical content. However, the assessment also identified certain limitations that we acknowledge transparently. Questions were sometimes less practical or clinical than would be ideal for real-world application. Answers could occasionally be overly general or broad, lacking the specificity that might be preferred in clinical contexts. Some questions lacked evidence-based medical reasoning, and there was insufficient grounding in evidence-based medicine principles in certain cases.</p>
<p>Despite these identified limitations, experts concluded that the QA data was sufficient for its intended purpose as supplementary training data for domain adaptation. The identified limitations do not significantly impact the utility of AnesQA for its intended purpose, which is demonstrating that domain-specific training data can improve anesthesiology reasoning performance. The effectiveness of our approach is empirically validated through the performance improvements observed in our training experiments, as shown in Table 6. Furthermore, our resource allocation strategy prioritized ensuring the quality of our main contribution (AnesBench), where we conducted comprehensive expert validation as detailed in our response to Limitation 1, while applying appropriate quality assurance measures to the supplementary training data.</p>
<h2 id="conclusion">Conclusion</h2>
<p>We believe these detailed responses comprehensively address the reviewer's concerns and provide the transparency requested regarding our methodology and quality assurance processes. The expert validation of our benchmark demonstrates rigorous quality control for our primary contribution, establishing AnesBench as a reliable evaluation tool for anesthesiology reasoning in large language models. While our QA generation methodology is not perfect, it is appropriate for its intended research purpose and follows established practices in the field for synthetic data generation in specialized medical domains.</p>
<p>We will incorporate these clarifications and additional details into the revised manuscript, particularly expanding Appendix J with the detailed expert review results and methodology. This will ensure that future readers have complete access to our quality assurance procedures and can fully understand the rigor applied to both our benchmark construction and supplementary data generation processes.</p>

</body>
</html>

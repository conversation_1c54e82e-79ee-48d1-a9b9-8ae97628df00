\begin{abstract}

% Our study comprehensively evaluates over 30 state-of-the-art large language models(LLMs) in the context of anesthesiology, focusing on their reasoning capabilities in knowledge retrieval (System 1 tasks) and complex decision-making (System 2 tasks). Existing models remain inadequate in addressing the complex reasoning required in specialized fields such as anesthesiology, despite advances in medical AI. \anesbench—a bilingual benchmark categorizing reasoning tasks into three levels (System 1, System 2, and hybrid System 1.x)—is introduced to systematically assess LLM performance. Model characteristics (e.g., scale, chain of thought length, and language transferability) and training methodologies (e.g., continuous pretraining and supervised fine-tuning) were evaluated for their impact on performance. Our findings underscore that larger model sizes significantly enhance System 1 tasks, while chain-of-thought reasoning length and multilingual capabilities substantially influence System 2 tasks. Evidence also indicates that supervised fine-tuning alone is insufficient for complex decision-making. Techniques such as knowledge distillation and test-time reasoning strategies (e.g., self-consistency, beam search, and MCTS) are crucial for enhancing performance in high-risk medical environments. A novel dataset for continuous pretraining and supervised fine-tuning was introduced to support our conclusions.

The application of large language models (LLMs) in the medical field has gained significant attention, yet their reasoning capabilities in more specialized domains like anesthesiology remain underexplored.
In this paper, we systematically evaluate the reasoning capabilities of LLMs in anesthesiology and analyze key factors influencing their performance. To this end, we introduce \anesbench, a cross-lingual benchmark designed to assess anesthesiology-related reasoning across three levels: factual retrieval (System 1), hybrid reasoning (System 1.x), and complex decision-making (System 2). Through extensive experiments, we first explore how model characteristics, including model scale, Chain of Thought (CoT) length, and language transferability, affect reasoning performance. Then, we further evaluate the effectiveness of different training strategies, leveraging our curated anesthesiology-related dataset, including continuous pre-training (CPT) and supervised fine-tuning (SFT). Additionally, we also investigate how the test-time reasoning techniques, such as Best-of-N sampling and beam search, influence reasoning performance, and assess the impact of reasoning-enhanced model distillation, specifically DeepSeek-R1.
% Our findings reveal that System 1 tasks benefit from model scaling, while System 2 reasoning relies more on structured CoT and targeted training. 
% Additionally, we demonstrate that test-time compute boosts reasoning in anesthesiology. 
We will publicly release \anesbench, along with our CPT and SFT training datasets and evaluation code at \url{https://github.com/MiliLab/AnesBench}.

% Our results demonstrate that supervised fine-tuning alone is insufficient for complex decision-making and that techniques such as knowledge distillation and test-time reasoning strategies (e.g., self-consistency, beam search, and Monte Carlo tree search) are critical for enhancing performance in high-risk medical settings. We introduced a novel dataset for continuous pretraining and supervised fine-tuning during this process.

% We present a comprehensive evaluation of large language models (LLMs) in the context of anesthesiology, focusing on their reasoning capabilities in both knowledge retrieval (System 1 tasks) and complex decision-making (System 2 tasks). Despite advancements in medical AI, existing models fall short in addressing the intricate reasoning needed for specialized fields like anesthesia. To systematically assess LLM performance, we introduce AnesBench, a benchmark that categorizes reasoning tasks into three levels: System 1, System 2, and a hybrid System 1.x. We evaluate the impact of model characteristics (e.g., scale, CoT length, and language transferability) and training methodologies (e.g., continuous pretraining and supervised fine-tuning) on performance. Our findings highlight that larger model scale improves System 1 tasks, while CoT length and multilingual capabilities significantly impact System 2 tasks. Additionally, we demonstrate that supervised fine-tuning alone is insufficient for complex decision-making, with techniques such as knowledge distillation and test-time reasoning strategies (e.g., Self-Consistency, Beam Search, and MCTS) being critical for enhancing performance in high-stakes medical contexts.

% 我们在麻醉学的背景下对大型语言模型(LLM)进行了全面的评估，重点是它们在知识检索的能力(系统1任务)和在复杂决策(系统2任务)方面的推理能力。尽管医学人工智能取得了进步，但现有模型在解决麻醉等专业领域所需的复杂推理方面存在不足。为了系统地评估LLM性能，我们引入了AnesBench，这是一种将推理任务分为三个级别的双语基准:系统1、系统2和混合系统1.x。我们评估了模型特征(例如，规模、成本长度和语言可转移性)和训练方法(例如，连续预训练和监督微调)对性能的影响。我们的发现强调了更大的模型大小显著改善了系统1的任务，而COT和多语言能力显著影响了系统2的任务。此外，我们证明，对于复杂的决策来说，仅仅有监督的微调是不够的，知识提炼和测试时推理策略(如自洽性、波束搜索和MCTS)等技术对于提高高风险医疗环境中的性能至关重要。 在此过程中，我们同时引入了一个新的持续预训练和监督微调数据集 

% 同时，证据表明，对于复杂的决策来说，仅仅有监督的微调是不够的，知识提炼和测试时推理策略(如自洽性、波束搜索和MCTS)等技术对于提高高风险医疗环境中的性能至关重要。 为了支撑我们的结论，我们同时引入了一个新的持续预训练和监督微调数据集。 
\end{abstract}

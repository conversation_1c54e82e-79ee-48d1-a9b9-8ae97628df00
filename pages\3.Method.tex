\section{Methodology}

\subsection{Benchmark Overview}

We construct \textbf{\anesbench} to assess LLMs' reasoning and decision-making in anesthesiology. The benchmark contains 4,427 anesthesiology questions in English. Each question is labeled with a three-level categorization of cognitive demands, and detailed examples and statistics are provided in Fig. \ref{fig:overview}. Each question also includes Chinese-English translations, enabling evaluation of LLMs' knowledge, application, and clinical reasoning abilities across diverse linguistic contexts. 

\subsubsection{Benchmark Collection}

\anesbench is constructed from authoritative sources, including American Board of Anesthesiology (ABA) examination materials, standardized textbooks, and validated online assessment tools. These resources provide diverse questions assessing factual knowledge, application-based decision-making, and computational problem-solving.

We incorporate the CAB ~\cite{CAB} as a supplementary resource. CAB contains over 8,000 multiple-choice questions (MCQs), 2,000 consultation-style question-answer (CQA) pairs, and 136 adversarially crafted questions designed to test model safety and robustness. Since evaluating specialized QA pairs involves subjective judgments and imperfect metrics, we include only the Anesthesiology Multiple Choice Question Answering (AMCQA) subset from CAB, as MCQs offer clear, standardized metrics.

\anesbench thus serves as a structured benchmark across multiple complexity levels. The incorporated CAB content functions as both a comparative reference to highlight differences in reasoning performance and a linguistic supplement for bilingual evaluation. This design allows for a more nuanced and robust analysis of LLMs' reasoning capabilities in anesthesiology. 

\paragraph{Data Leakage Detection}  After the construction of \anesbench, we conduct a comprehensive data leakage analysis, using a data leakage detection algorithm~\cite{DataLeakageDetect} designed for multiple-choice questions. As detailed in Appendix~\ref{sec:dataLeakage}, our analysis confirms that \anesbench exhibits minimal data leakage, underscoring its reliability for assessing anesthetic reasoning in LLMs.

\subsubsection{Benchmark Categorization}


To provide a fine-grained assessment of LLMs' reasoning capabilities in anesthesiology, we categorize benchmarks, including \anesbench and AMCQA, into three distinct levels based on the cognitive demands. We employ DeepSeek-R1~\cite{Deepseek-R1} to categorize questions, the full prompt for categorization is provided in Appendix~\ref{appendix:questionCategorization}.
Specifically, we adopt the System 1 and System 2 framework to characterize questions with different cognitive processes~\cite{Fast&SlowThinking}. System 1 represents fast and intuitive thinking, typically applied to straightforward knowledge-retrieval tasks, whereas System 2 denotes slow, logical, and conscious thinking, essential for complex reasoning and decision-making. Building on this framework, we further incorporate the concept of System 1.x~\cite{System1.x}, which blends elements of both System 1 and System 2. The question examples of these three levels can be found in Fig. \ref{fig:overview}.


\subsubsection{Cross-Language Benchmarking}
To mitigate the potential impact of language proficiency on reasoning evaluation, we adopt a cross-lingual benchmarking approach. 
We construct two bilingual benchmark sets by translating \anesbench and AMCQA in complementary directions. Specifically, \anesbench, originally developed in English, is translated into Chinese (EN2CN), while AMCQA, originally in Chinese, is translated into English (CN2EN). This process ensures that each dataset exists in both languages, enabling direct cross-lingual comparisons of model performance. This setup enables analysis of how language transferability affects complex decision-making processes .

We employ GPT-4o~\cite{gpt4o-0513} to achieve high-quality translations, which preserve the original meaning, logical structure, and domain-specific terminology. Furthermore, to enhance translation accuracy, we perform post-translation validation, including expert review and consistency checks against standardized anesthesiology references.
The full prompt for translation is provided in Appendix~\ref{appendix:questionTranslation}.


% \subsubsection{Zero-Shot CoT for Reasoning Evaluation}
% Given that our evaluation focuses on reasoning-intensive System 2 tasks, we applied CoT prompting to encourage structured, step-by-step reasoning. In our evaluation, we employed a Zero-Shot CoT~\cite{Zero-Shot-COT} prompting strategy, which enhances model reasoning by incorporating explicit reasoning cues, such as ``Let's think step by step'', without relying on examples. 

% Additionally, recognizing the variability in instruction adherence among different models, we opted not to impose rigid output format constraints. This choice ensures a fair comparison across models with diverse instruction-following behaviors, allowing for a more accurate assessment of their reasoning proficiency in complex anesthetic decision-making scenarios.

\subsection{Evaluation of Training \& Reasoning}

\subsubsection{Training Dataset Collection.} 

For the evaluation on the impact of Continuous Pre-training and Supervised Fine-Tuning, we construct specialized training datasets tailored to the anesthesiology domain using two strategies. First, we extract domain-specific data from existing large-scale corpora like FineWeb~\cite{fineweb}, using keyword frequency-based filtering. Second, we gather anesthesiology-related research papers from PubMed~\footnote{\href{https://pubmed.ncbi.nlm.nih.gov/}{PubMed: https://pubmed.ncbi.nlm.nih.gov/}} and perform extensive data cleaning, such as content removal, format standardization, and relevance filtering, to retain high-quality, domain-specific textual data.

For CPT, we merge and utilize the curated dataset, referred to as \textbf{AnesCorpus}, from these two sources to enhance the model's domain-specific knowledge. By pre-training on this specialized corpus, we aim to improve the model’s foundational understanding of anesthesiology concepts, terminology, and common clinical scenarios. The CPT corpus is further illustrated by the word cloud analysis in Appendix~\ref{sec:corpus}, demonstrating its strong alignment in anesthesiology-related topics.


Since domain-specific SFT requires high-quality QA data, which is often scarce and difficult to obtain, we employ a data synthesis pipeline leveraging LLMs. Specifically, we segment papers into small chunks based on length and section structure, then use LLaMA3.3-70B-Instruct~\cite{Llama3} to generate self-contained questions for each chunk. Next, we employ Qwen2.5-72B-Instruct~\cite{qwen2.5} to filter and answer the most informative and domain-relevant questions. Finally, we translate the QA pairs into Chinese using GPT-4o-mini, creating a bilingual dataset for fine-tuning. We name the entire question-answering dataset as \textbf{AnesQA}. The specifications of the \textbf{AnesCorpus} and the \textbf{AnesQA} are presented in Appendix~\ref{appendix:datasets} Table~\ref{tab:cpt_sft}.


\subsubsection{Training \& Reasoning Process.}
We employ a two-stage training strategy, including CPT and SFT.
We first conduct CPT on Qwen2.5-7B-Base~\cite{qwen2.5} using our curated anesthesia-related corpus. The objective of CPT is to enhance the model’s foundational knowledge in anesthesiology by exposing it to high-quality domain-specific text. 
For SFT, we focus on aligning the model with the structure of multiple-choice question tasks while reinforcing its ability to handle anesthesia-specific inquiries. To achieve this, we incorporate the medical-o1~\cite{huatuogpt} dataset alongside our \textbf{AnesQA} dataset. 

Following training, we implement an advanced test-time compute process inspired by Marco-o1~\cite{outputPattern_1}, designed to enhance multi-step reasoning. Specifically, we structure CoT reasoning into mini-steps with a predefined token budget, ensuring a granular and interpretable reasoning pathway. To further improve response quality, we incorporate a self-reflection mechanism~\cite{outputPattern_1}, prompting the model to critically reassess and refine its initial outputs. Additionally, we employ tree search strategies, including Best-of-N~\cite{yao2023tree} and Beam Search~\cite{beamSearch}, enabling the model to explore multiple reasoning trajectories. The final answer is determined through the majority vote mechanism, selecting the most coherent and accurate response. 

\subsubsection{Distillation}
Beyond CPT and SFT, we further evaluate the impact of reasoning-enhanced model distillation. Specifically, we assess models distilled from DeepSeek-R1~\cite{Deepseek-R1} and compare them with their original instruct versions. As one of the state-of-the-art reasoning models, DeepSeek-R1 serves as a strong reference point for evaluating the efficacy of distillation in enhancing reasoning performance.

The distillation process was conducted by the official DeepSeek team. For these distilled models, only SFT was applied, without incorporating reinforcement learning (RL)~\cite{Deepseek-R1}. While RL could potentially further enhance performance, the primary objective of this evaluation is to demonstrate the effectiveness of distillation as a technique for transferring reasoning abilities to smaller models. Future research may explore the additional benefits of RL in complementing this distillation process.


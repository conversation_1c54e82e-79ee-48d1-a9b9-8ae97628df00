\section{Related Work}
\label{sec:related_work}

\subsection{Reasoning LLMs}

% 推理往往 refers to answering questions involving complex, multi-step processes with intermediate steps. 而Openai o1/o3以及Deepseek R1等推理LLMs的出现激起了人们对驱动模型提升LLMs推理能力的因素研究的热情。现有的研究可以分为（1）分析模型包括基本characteristic和输出结构的特征分析和（2）多种训练和推理范式改进。 例如，当前有研究表明LLMs在解决数学或者推理问题时往往会产生更长的输出。此外，在输出中表现出强烈探索倾向或者在微观上有自己检查结构的LLMs往往会有更强的推理能力。在训练和推理范式上， 以强化学习微调（RFT）、self-improvement等方法和以MCTS为代表的多种结构化搜索方法也显示出巨大的发展潜力。

% 此外，不同的模型scale也被证明在训练动力学和推理特征上具有巨大差异。

Reasoning refers to answering questions that involve complex, multi-step processes with intermediate steps~\cite{reasoning_definition}. The emergence of reasoning LLMs such as OpenAI o1/o3~\cite{openai_o1,o3-mini} and Deepseek R1~\cite{Deepseek-R1} has spurred interest in enhancing LLM reasoning ability. Existing research can be divided into two categories: (1) analyses of models' basic characteristics and output structures, and (2) improvements in training and inference paradigms. Recent research indicates that LLMs tend to generate longer outputs when solving mathematical or reasoning problems~\cite{TowardsSystem2ReasoninginLLMs}. Model performance benefits from scaling the length of the reasoning process~\cite{COT_length_2,COT_length_medical}. Models that exhibit strong exploratory tendencies or incorporate intrinsic verification mechanisms in their outputs often demonstrate enhanced reasoning capabilities~\cite{TowardsSystem2ReasoninginLLMs,Deepseek-R1}. Moreover, different model scales have been demonstrated to exhibit significant differences in training dynamics and reasoning characteristics~\cite{openai_o1,o3-mini,outputPattern_1}. On the other hand, training and inference paradigms, such as reinforcement learning fine-tuning (RFT)~\cite{RFT_1,Deepseek-R1,KIMI1.5}, self-improvement methods~\cite{selfImprovement_1,selfImprovement_2,selfImprovement_3}, and structured search techniques exemplified by MCTS~\cite{MCTS_1,MCTS_2,MCTS_3}, have shown significant development potential.

\subsection{Anesthesia-related Benchmarks and LLMs}

Anesthesiology, due to its unique interdisciplinary nature, is often implicitly classified under surgery, dentistry, or similar categories~\cite{AnesBenchmark_implicit_1,AnesBenchmark_implicit_2,AnesBenchmark_implicit_3,AnesBenchmark_implicit_4}, even in benchmarks that categorize based on different departments. Some benchmarks explicitly treat anesthesiology as a separate category~\cite{AnesBenchmark_explicit_1,AnesBenchmark_explicit_2,CMB}, while some studies employ existing anesthesiology-related questions~\cite{smallEvaluationAnes_1,smallEvaluationAnes_2,smallEvaluationAnes_3,smallEvaluationAnes_4,smallEvaluationAnes_6} (e.g., exam questions from American Society of Anesthesiologists, ASA and Japanese Society of Anesthesiologists, JSA) to evaluate LLMs. However, the overall scale of these evaluations for anesthesiology remains very limited. Chinese Anesthesiology Benchmark (CAB)~\cite{CAB} is the first to focus primarily on anesthesiology. Nevertheless, CAB still overlooks the specific challenges of reasoning and decision-making in anesthesiology, and its use of exclusively Chinese questions restricts its broader applicability. Moreover, Hypnos and some works~\cite{AnesLLMs_Hypnos,AnesLLMs_1}  have attempted to use anesthesiology-related corpora for supervised fine-tuning to improve question-answering abilities, but they have not significantly enhanced the models’ reasoning capabilities.

%  当前，在大部分的医疗LLM benchmark中，麻醉学没有被显式地区分出来。即使是在以不同科室为分类依据的benchmark中，麻醉学由于其与多学科相关联的特殊定位，也往往会被隐式地分类在外科，口腔科等分类中~\cite{}。 部分benchmark虽然显式地将麻醉学作为一个单独分类或者部分些工作使用现有麻醉学相关题目（如美国麻醉医师学会，日本麻醉学会的考试题目）用于评估LLMs。但是，对于麻醉学，这些评估整体体量仍然非常有限。而CAB，是第一个以麻醉医学为主体的benchmark。但是同时，其仍然忽视了推理以及决策问题在麻醉学中的特殊性，以及纯中文的问题限制了其更广泛的使用场景。此外，已经有一些工作~\cite{}，如Hypnos~\cite{}等尝试使用麻醉学相关语料进行SFT提高问答能力，但没有显著提高模型的推理能力。

% \subsection{General Medical LLM}
%大型语言模型的最新进展导致了许多为医学领域量身定制的方法的出现。这些模型通常利用变压器架构，并在对目标医疗数据集进行监督微调(SFT)之前，在广泛的通用语料库上进行预训练。这种两步训练模式显著提高了在各种问答任务中产生临床相关和连贯反应的能力。
%虽然早期的模型主要集中在事实回忆和信息检索，但最近的工作已经转向整合复杂的推理能力。整合了思维链(CoT)提示和其他推理机制的方法在处理更复杂的临床场景方面显示出了前景。然而，在更广泛的医疗人工智能社区中，增强推理仍然是一个开放的挑战。
% Recent advances in large language models have led to the emergence of numerous approaches tailored for the medical domain~\cite{}. These models typically leverage transformer architectures and are pre-trained on extensive general corpora before undergoing supervised fine-tuning (SFT) on targeted medical datasets~\cite{}. This two-step training paradigm has significantly improved the ability to generate clinically relevant and coherent responses in various question-answering tasks.
% While early models primarily focused on factual recall and information retrieval, recent work has shifted towards integrating complex reasoning capabilities. Approaches that incorporate chain-of-thought (CoT) prompting and other inference mechanisms have shown promise in addressing more intricate clinical scenarios~\cite{}. However, enhancing reasoning remains an open challenge in the broader medical AI community.

% \subsection{Anesthesia-Specific LLM}
% 麻醉学涉及高风险决策，不仅需要事实知识检索，还需要不确定性下的复杂推理。然而，现有的医学LLM，如修普诺斯等，主要集中在通过监督微调(SFT)来提高问答性能，而没有明显提高推理能力。此外，区分知识检索(系统1)和复杂推理(系统2)的综合评估仍然有限。以前对医学模型的评估很大程度上依赖于一般的质量保证基准，这无法捕捉麻醉实践固有的复杂决策挑战。因此，医学LLMs在麻醉相关任务中的推理能力仍不清楚。
% Anesthesiology involves high-stakes decision-making, requiring not only factual knowledge retrieval but also complex reasoning under uncertainty. However, existing medical LLMs, such as Hypnos~\cite{}, primarily focus on improving question-answering (QA) performance through supervised fine-tuning (SFT) without explicitly enhancing reasoning capabilities. Furthermore, comprehensive evaluations that differentiate between knowledge retrieval (System 1) and complex reasoning (System 2) remain limited~\cite{}. Previous evaluations of medical models have largely relied on general QA benchmarks, which fail to capture the intricate decision-making challenges inherent to anesthesia practice. As a result, the reasoning abilities of medical LLMs in anesthesia-related tasks remain unclear.

\section{Related Work}
\label{sec:related_work}

\subsection{Reasoning LLMs}

Reasoning refers to solving problems involve complex, multi-step processes with intermediate steps~\cite{reasoning_definition}. The emergence of reasoning LLMs such as OpenAI o1/o3~\cite{openai_o1,o3-mini} and Deepseek R1~\cite{Deepseek-R1} has spurred interest in enhancing LLM's reasoning ability. 
% Existing research can be divided into two categories: (1) analyses of models' basic characteristics and output structures, and (2) improvements in training and inference paradigms. 
Recent research indicates that LLMs tend to generate longer outputs when solving mathematical or reasoning problems~\cite{TowardsSystem2ReasoninginLLMs}. Model performance benefits from scaling the length of the reasoning process~\cite{COT_length_2,COT_length_medical}. 
Models that exhibit strong exploratory tendencies or incorporate intrinsic verification mechanisms in their outputs often demonstrate enhanced reasoning capabilities~\cite{TowardsSystem2ReasoninginLLMs,Deepseek-R1}. Moreover, different model scales have been demonstrated to exhibit significant differences in training dynamics and reasoning characteristics~\cite{openai_o1,o3-mini,outputPattern_1}. On the other hand, training and inference paradigms, such as reinforcement learning fine-tuning (RFT)~\cite{RFT_1,Deepseek-R1,KIMI1.5}, self-improvement methods~\cite{selfImprovement_1,selfImprovement_2,selfImprovement_3}, and structured search techniques exemplified by MCTS~\cite{MCTS_1,MCTS_2,MCTS_3}, have shown significant development potential.

\subsection{Anesthesia-related Benchmarks and LLMs}

Anesthesiology, due to its unique interdisciplinary nature, is often implicitly classified under surgery, dentistry, or similar categories~\cite{AnesBenchmark_implicit_1,AnesBenchmark_implicit_2,AnesBenchmark_implicit_3}, even in benchmarks that categorize based on different departments. Some benchmarks explicitly treat anesthesiology as a separate category~\cite{AnesBenchmark_explicit_2,CMB}, while some studies employ existing anesthesiology-related questions~\cite{smallEvaluationAnes_1,smallEvaluationAnes_2,smallEvaluationAnes_3,smallEvaluationAnes_4,smallEvaluationAnes_6} (e.g., exam questions from American Society of Anesthesiologists, ASA and Japanese Society of Anesthesiologists, JSA) to evaluate LLMs. However, the overall scale of these evaluations for anesthesiology remains very limited. Chinese Anesthesiology Benchmark (CAB)~\cite{CAB} is the first to focus primarily on anesthesiology. Nevertheless, CAB still overlooks the specific challenges of reasoning and decision-making in anesthesiology, and its language limits in Chinese. Moreover, Hypnos and some works~\cite{AnesLLMs_Hypnos,AnesLLMs_1} attempts to use anesthesiology-related corpora for supervised fine-tuning to improve question-answering abilities, but they have not significantly enhanced the reasoning capabilities.

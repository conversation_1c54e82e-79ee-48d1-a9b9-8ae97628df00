@misc{qwen3,
    title  = {Qwen3},
    url    = {https://qwenlm.github.io/blog/qwen3/},
    author = {Qwen Team},
    month  = {April},
    year   = {2025}
}

@misc{llama4,
    title  = {The Llama 4 herd: The beginning of a new era of natively multimodal AI innovation},
    url    = {https://ai.meta.com/blog/llama-4-multimodal-intelligence/},
    author = {Meta},
    month  = {April},
    year   = {2025}
}

@article{gemma3,
  title={Gemma 3 technical report},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>{\'e}, <PERSON> and <PERSON><PERSON><PERSON>{\`e}re, <PERSON><PERSON> and others},
  journal={arXiv preprint arXiv:2503.19786},
  year={2025}
}

@article{LongOutputHigherScoreCOT,
  title={Concise thoughts: Impact of output length on llm reasoning and cost},
  author={<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON>},
  journal={arXiv preprint arXiv:2407.19825},
  year={2024}
}
@inproceedings{Zero-Shot-COT,
 author = {Wei, <PERSON> and Wang, Xuezhi and Schuurmans, Dale and Bosma, Maarten and ichter, brian and Xia, Fei and Chi, Ed and Le, Quoc V and Zhou, Denny},
 booktitle = {NeurIPS},
 title = {Chain-of-Thought Prompting Elicits Reasoning in Large Language Models},
 year = {2022}
}
@article{ChatGPTDifferentLanguageDifferentPerforamce,
  title={Don't trust ChatGPT when your question is not in English: a study of multilingual abilities and types of LLMs},
  author={Zhang, Xiang and Li, Senyu and Hauer, Bradley and Shi, Ning and Kondrak, Grzegorz},
  journal={arXiv preprint arXiv:2305.16339},
  year={2023}
}
@article{Llama3,
  title={The llama 3 herd of models},
  author={Grattafiori, Aaron and Dubey, Abhimanyu and Jauhri, Abhinav and Pandey, Abhinav and Kadian, Abhishek and Al-Dahle, Ahmad and Letman, Aiesha and Mathur, Akhil and Schelten, Alan and Vaughan, Alex and others},
  journal={arXiv preprint arXiv:2407.21783},
  year={2024}
}
@article{Fast&SlowThinking,
  title={Fast and slow thinking},
  author={Kahneman, Daniel},
  journal={Allen Lane and Penguin Books, New York},
  year={2011}
}
@article{System1.x,
  title={System-1. x: Learning to balance fast and slow planning with language models},
  author={Saha, Swarnadeep and Prasad, Archiki and Chen, Justin Chih-Yao and Hase, Peter and Stengel-Eskin, Elias and Bansal, Mohit},
  journal={arXiv preprint arXiv:2407.14414},
  year={2024}
}
@inproceedings{CAB,
  title={Benchmarking Medical LLMs on Anesthesiology: A Comprehensive Dataset in Chinese},
  author={Zhou, Bohao and Zhan, Yibing and Wang, Zhonghai and Li, Yanhong and Zhang, Chong and Yu, Baosheng and Ding, Liang and Jin, Hua and Liu, Weifeng and Wang, Xiongbin and others},
  booktitle={TETCI},
  year={2025},
  publisher={IEEE}
}
@article{SmallModelStuggleLearnReasoning,
  title={Small Models Struggle to Learn from Strong Reasoners},
  author={Li, Yuetai and Yue, Xiang and Xu, Zhangchen and Jiang, Fengqing and Niu, Luyao and Lin, Bill Yuchen and Ramasubramanian, Bhaskar and Poovendran, Radha},
  journal={arXiv preprint arXiv:2502.12143},
  year={2025}
}
@article{DataLeakageDetect,
  title={Training on the Benchmark Is Not All You Need},
  author={Ni, Shiwen and Kong, Xiangtao and Li, Chengming and Hu, Xiping and Xu, Ruifeng and Zhu, Jia and Yang, Min},
  journal={arXiv preprint arXiv:2409.01790},
  year={2024}
}
@inproceedings{MMLU,
  author       = {Xuezhi Wang and
                  Jason Wei and
                  Dale Schuurmans and
                  Quoc V. Le and
                  Ed H. Chi and
                  Sharan Narang and
                  Aakanksha Chowdhery and
                  Denny Zhou},
  title        = {Self-Consistency Improves Chain of Thought Reasoning in Language Models},
  booktitle    = {ICLR},
  year         = {2023},
}
@inproceedings{CMMLU,
  author       = {Haonan Li and
                  Yixuan Zhang and
                  Fajri Koto and
                  Yifei Yang and
                  Hai Zhao and
                  Yeyun Gong and
                  Nan Duan and
                  Timothy Baldwin},
  title        = {CMMLU: Measuring massive multitask language understanding in Chinese},
  booktitle    = {ACL},
  year         = {2024}
}
@inproceedings{C-Eval,
 author = {Huang, Yuzhen and Bai, Yuzhuo and Zhu, Zhihao and Zhang, Junlei and Zhang, Jinghan and Su, Tangjun and Liu, Junteng and Lv, Chuancheng and Zhang, Yikai and lei, jiayi and Fu, Yao and Sun, Maosong and He, Junxian},
 booktitle = {NeurIPS},
 title = {C-Eval: A Multi-Level Multi-Discipline Chinese Evaluation Suite for Foundation Models},
 year = {2023}
}
@inproceedings{CMB,
  title={CMB: A Comprehensive Medical Benchmark in Chinese},
  author={Wang, Xidong and Chen, Guiming and Dingjie, Song and Zhiyi, Zhang and Chen, Zhihong and Xiao, Qingying and Chen, Junying and Jiang, Feng and Li, Jianquan and Wan, Xiang and others},
  booktitle={NAACL},
  year={2024}
}
@article{AnesLLMs_1,
  title={The Operating and Anesthetic Reference Assistant (OARA): A fine-tuned large language model for resident teaching},
  author={Guthrie, Estefania and Levy, Dominique and Del Carmen, Gabriel},
  journal={The American Journal of Surgery},
  volume={234},
  pages={28--34},
  year={2024},
  publisher={Elsevier}
}
@article{AnesLLMs_Hypnos,
  title={Towards Training A Chinese Large Language Model for Anesthesiology},
  author={Wang, Zhonghai and Jiang, Jie and Zhan, Yibing and Zhou, Bohao and Li, Yanhong and Zhang, Chong and Ding, Liang and Jin, Hua and Peng, Jun and Lin, Xu and others},
  journal={arXiv preprint arXiv:2403.02742},
  year={2024}
}
@article{smallEvaluationAnes_1,
  title={Artificial Intelligence for Anesthesiology Board--Style Examination Questions: Role of Large Language Models},
  author={Khan, Adnan A and Yunus, Rayaan and Sohail, Mahad and Rehman, Taha A and Saeed, Shirin and Bu, Yifan and Jackson, Cullen D and Sharkey, Aidan and Mahmood, Feroze and Matyal, Robina},
  journal={Journal of Cardiothoracic and Vascular Anesthesia},
  volume={38},
  number={5},
  pages={1251--1259},
  year={2024},
  publisher={Elsevier}
}
@article{smallEvaluationAnes_2,
  title={Clinical knowledge and reasoning abilities of AI large language models in anesthesiology: A comparative study on the American Board of Anesthesiology examination},
  author={Angel, Mirana C and Rinehart, Joseph B and Canneson, Maxime P and Baldi, Pierre},
  journal={Anesthesia \& Analgesia},
  volume={139},
  number={2},
  pages={10--1213},
  year={2022},
  publisher={LWW}
}
@article{smallEvaluationAnes_3,
  title={Assessment of ChatGPT success with specialty medical knowledge using anaesthesiology board examination practice questions},
  author={Shay, Denys and Kumar, Bhawesh and Bellamy, David and Palepu, Anil and Dershwitz, Mark and Walz, Jens M and Schaefer, Maximilian S and Beam, Andrew},
  journal={BJA: British Journal of Anaesthesia},
  volume={131},
  number={2},
  pages={e31},
  year={2023}
}
@article{smallEvaluationAnes_4,
  title={ChatGPT’s performance on JSA-certified anesthesiologist exam},
  author={Kinoshita, Michiko and Komasaka, Mizuki and Tanaka, Katsuya},
  journal={Journal of Anesthesia},
  volume={38},
  number={2},
  pages={282--283},
  year={2024},
  publisher={Springer}
}
@article{smallEvaluationAnes_6,
  title={Comparison of ChatGPT vs. Bard to anesthesia-related queries},
  author={Patnaik, Sourav S and Hoffmann, Ulrike},
  journal={medRxiv},
  pages={2023--06},
  year={2023},
  publisher={Cold Spring Harbor Laboratory Press}
}
@article{AnesBenchmark_explicit_1,
  title={MedDM: LLM-executable clinical guidance tree for clinical decision-making},
  author={Li, Binbin and Meng, Tianxin and Shi, Xiaoming and Zhai, Jie and Ruan, Tong},
  journal={arXiv preprint arXiv:2312.02441},
  year={2023}
}
@article{AnesBenchmark_implicit_1,
  title={COGNET-MD, an evaluation framework and dataset for Large Language Model benchmarks in the medical domain},
  author={Panagoulias, Dimitrios P and Papatheodosiou, Persephone and Palamidas, Anastasios P and Sanoudos, Mattheos and Tsoureli-Nikita, Evridiki and Virvou, Maria and Tsihrintzis, George A},
  journal={arXiv preprint arXiv:2405.10893},
  year={2024}
}
@article{AnesBenchmark_implicit_2,
  title={AfriMed-QA: A Pan-African, Multi-Specialty, Medical Question-Answering Benchmark Dataset},
  author={Olatunji, Tobi and Nimo, Charles and Owodunni, Abraham and Abdullahi, Tassallah and Ayodele, Emmanuel and Sanni, Mardhiyah and Aka, Chinemelu and Omofoye, Folafunmi and Yuehgoh, Foutse and Faniran, Timothy and others},
  journal={arXiv preprint arXiv:2411.15640},
  year={2024}
}
@article{AnesBenchmark_explicit_2,
  title={Polish Medical Exams: A new dataset for cross-lingual medical knowledge transfer assessment},
  author={Grzybowski, {\L}ukasz and Pokrywka, Jakub and Ciesi{\'o}{\l}ka, Micha{\l} and Kaczmarek, Jeremi I and Kubis, Marek},
  journal={arXiv preprint arXiv:2412.00559},
  year={2024}
}
@inproceedings{AnesBenchmark_implicit_3,
  title={Bridging the gap between consumers’ medication questions and trusted answers},
  author={Abacha, Asma Ben and Mrabet, Yassine and Sharp, Mark and Goodwin, Travis R and Shooshan, Sonya E and Demner-Fushman, Dina},
  booktitle={MEDINFO},
  year={2019},
}
@article{AnesBenchmark_implicit_4,
  title={Medbench: A comprehensive, standardized, and reliable benchmarking system for evaluating chinese medical large language models},
  author={Liu, Mianxin and Hu, Weiguo and Ding, Jinru and Xu, Jie and Li, Xiaoyang and Zhu, Lifeng and Bai, Zhian and Shi, Xiaoming and Wang, Benyou and Song, Haitao and others},
  journal={Big Data Mining and Analytics},
  volume={7},
  number={4},
  pages={1116--1128},
  year={2024},
  publisher={TUP}
}
@misc{openai_o1,
    title = {Learning to reason with LLMs},
    url = {https://openai.com/index/learning-to-reason-with-llms/},
    author = {OpenAI},
    month = {September},
    year = {2024}
}
@misc{o3-mini,
    title = {OpenAI o3-mini},
    url = {https://openai.com/index/openai-o3-mini/},
    author = {OpenAI},
    month = {January},
    year = {2025}
}
@article{Deepseek-R1,
  title={DeepSeek-R1: Incentivizing Reasoning Capability in LLMs via Reinforcement Learning},
  author={Guo, Daya and Yang, Dejian and Zhang, Haowei and Song, Junxiao and Zhang, Ruoyu and Xu, Runxin and Zhu, Qihao and Ma, Shirong and Wang, Peiyi and Bi, Xiao and others},
  journal={arXiv preprint arXiv:2501.12948},
  year={2025}
}
@article{reasoning_definition,
  title={From system 1 to system 2: A survey of reasoning large language models},
  author={Li, Zhong-Zhi and Zhang, Duzhen and Zhang, Ming-Liang and Zhang, Jiaxin and Liu, Zengyan and Yao, Yuxuan and Xu, Haotian and Zheng, Junhao and Wang, Pei-Jie and Chen, Xiuyi and others},
  journal={arXiv preprint arXiv:2502.17419},
  year={2025}
}
@article{TowardsSystem2ReasoninginLLMs,
  title={Towards System 2 Reasoning in LLMs: Learning How to Think With Meta Chain-of-Though},
  author={Xiang, Violet and Snell, Charlie and Gandhi, Kanishk and Albalak, Alon and Singh, Anikait and Blagden, Chase and Phung, Duy and Rafailov, Rafael and Lile, Nathan and Mahan, Dakota and others},
  journal={arXiv preprint arXiv:2501.04682},
  year={2025}
}
@article{COT_length_2,
  title={Reflection-Window Decoding: Text Generation with Selective Refinement},
  author={Tang, Zeyu and Chen, Zhenhao and Li, Loka and Song, Xiangchen and Deng, Yunlong and Shen, Yifan and Chen, Guangyi and Spirtes, Peter and Zhang, Kun},
  journal={arXiv preprint arXiv:2502.03678},
  year={2025}
}
@article{COT_length_medical,
  title={O1 Replication Journey--Part 3: Inference-time Scaling for Medical Reasoning},
  author={Huang, Zhongzhen and Geng, Gui and Hua, Shengyi and Huang, Zhen and Zou, Haoyang and Zhang, Shaoting and Liu, Pengfei and Zhang, Xiaofan},
  journal={arXiv preprint arXiv:2501.06458},
  year={2025}
}
@article{outputPattern_1,
  title={Marco-o1: Towards open reasoning models for open-ended solutions},
  author={Zhao, Yu and Yin, Huifeng and Zeng, Bo and Wang, Hao and Shi, Tianqi and Lyu, Chenyang and Wang, Longyue and Luo, Weihua and Zhang, Kaifu},
  journal={arXiv preprint arXiv:2411.14405},
  year={2024}
}
@article{MCTS_1,
  title={Forest-of-thought: Scaling test-time compute for enhancing LLM reasoning},
  author={Bi, Zhenni and Han, Kai and Liu, Chuanjian and Tang, Yehui and Wang, Yunhe},
  journal={arXiv preprint arXiv:2412.09078},
  year={2024}
}
@article{MCTS_2,
  title={Treebon: Enhancing inference-time alignment with speculative tree-search and best-of-n sampling},
  author={Qiu, Jiahao and Lu, Yifu and Zeng, Yifan and Guo, Jiacheng and Geng, Jiayi and Wang, Huazheng and Huang, Kaixuan and Wu, Yue and Wang, Mengdi},
  journal={arXiv preprint arXiv:2410.16033},
  year={2024}
}
@article{MCTS_3,
  title={Codetree: Agent-guided tree search for code generation with large language models},
  author={Li, Jierui and Le, Hung and Zhou, Yingbo and Xiong, Caiming and Savarese, Silvio and Sahoo, Doyen},
  journal={arXiv preprint arXiv:2411.04329},
  year={2024}
}
@inproceedings{selfImprovement_1,
  title        = {STaR: Bootstrapping Reasoning With Reasoning},
   author = {Zelikman, Eric and Wu, Yuhuai and Mu, Jesse and Goodman, Noah},
 booktitle = {NeurIPS},
 year = {2022}
}
@article{selfImprovement_2,
  title={Reinforced self-training (rest) for language modeling},
  author={Gulcehre, Caglar and Paine, Tom Le and Srinivasan, Srivatsan and Konyushkova, Ksenia and Weerts, Lotte and Sharma, Abhishek and Siddhant, Aditya and Ahern, Alex and Wang, Miaosen and Gu, Chenjie and others},
  journal={arXiv preprint arXiv:2308.08998},
  year={2023}
}
@article{selfImprovement_3,
  title={Beyond human data: Scaling self-training for problem-solving with language models},
  author={Singh, Avi and Co-Reyes, John D and Agarwal, Rishabh and Anand, Ankesh and Patil, Piyush and Garcia, Xavier and Liu, Peter J and Harrison, James and Lee, Jaehoon and Xu, Kelvin and others},
  issn={2835-8856},
  journal={Transactions on Machine Learning Research},
  year={2024}
}
@article{KIMI1.5,
  title={Kimi k1. 5: Scaling Reinforcement Learning with LLMs},
  author={Team, Kimi and Du, Angang and Gao, Bofei and Xing, Bowei and Jiang, Changjiu and Chen, Cheng and Li, Cheng and Xiao, Chenjun and Du, Chenzhuang and Liao, Chonghua and others},
  journal={arXiv preprint arXiv:2501.12599},
  year={2025}
}
@article{RFT_1,
  title={Reft: Reasoning with reinforced fine-tuning},
  author={Luong, Trung Quoc and Zhang, Xinbo and Jie, Zhanming and Sun, Peng and Jin, Xiaoran and Li, Hang},
  journal={arXiv preprint arXiv:2401.08967},
  year={2024}
}
@article{medicalLagsBehind,
  title={Large language models need holistically thought in medical conversational qa},
  author={Weng, Yixuan and Li, Bin and Xia, Fei and Zhu, Minjun and Sun, Bin and He, Shizhu and Liu, Kang and Zhao, Jun},
  journal={arXiv preprint arXiv:2305.05410},
  year={2023}
}
@article{LLMSuccess,
  title={A survey on medical large language models: Technology, application, trustworthiness, and future directions},
  author={Liu, Lei and Yang, Xiaoyan and Lei, Junchi and Liu, Xiaoyang and Shen, Yue and Zhang, Zhiqiang and Wei, Peng and Gu, Jinjie and Chu, Zhixuan and Qin, Zhan and others},
  journal={arXiv preprint arXiv:2406.03712},
  year={2024}
}
@article{language_trans,
  title={Lost in translation: Large language models in non-English content analysis},
  author={Nicholas, Gabriel and Bhatia, Aliya},
  journal={arXiv preprint arXiv:2306.07377},
  year={2023}
}
@inproceedings{CPT,
    title={Continual Learning of Language Models}, 
    author={Ke, Zixuan and Shao, Yijia and Lin, Haowei and Konishi, Tatsuya and Kim, Gyuhak and Liu, Bing}, 
    booktitle={ICLR}, 
    year={2023}
}
@inproceedings{SFT,
 author = {Ouyang, Long and Wu, Jeffrey and Jiang, Xu and Almeida, Diogo and Wainwright, Carroll and Mishkin, Pamela and Zhang, Chong and Agarwal, Sandhini and Slama, Katarina and Ray, Alex and Schulman, John and Hilton, Jacob and Kelton, Fraser and Miller, Luke and Simens, Maddie and Askell, Amanda and Welinder, Peter and Christiano, Paul F and Leike, Jan and Lowe, Ryan},
 booktitle = {NeurIPS},
 title = {Training language models to follow instructions with human feedback},
 year = {2022}
}
@article{knowledge_dis,
  title={A survey on knowledge distillation of large language models},
  author={Xu, Xiaohan and Li, Ming and Tao, Chongyang and Shen, Tao and Cheng, Reynold and Li, Jinyang and Xu, Can and Tao, Dacheng and Zhou, Tianyi},
  journal={arXiv preprint arXiv:2402.13116},
  year={2024}
}
@inproceedings{self-consistency,
 author       = {Xuezhi Wang and
                  Jason Wei and
                  Dale Schuurmans and
                  Quoc V. Le and
                  Ed H. Chi and
                  Sharan Narang and
                  Aakanksha Chowdhery and
                  Denny Zhou},
  title        = {Self-Consistency Improves Chain of Thought Reasoning in Language Models},
  booktitle    = {ICLR},
  year         = {2023}
}
@inproceedings{beamSearch,
    title = "Beam Search Strategies for Neural Machine Translation",
    author = "Freitag, Markus  and
      Al-Onaizan, Yaser",
    booktitle = "ACL",
    year = "2017",
}
@misc{gpt4o-0513,
    title = {Hello GPT-4o},
    url = {https://openai.com/index/hello-gpt-4o/},
    author = {OpenAI},
    month = {May},
    year = {2024}
}
@article{Deepseek-v3,
  title={Deepseek-v3 technical report},
  author={Liu, Aixin and Feng, Bei and Xue, Bing and Wang, Bingxuan and Wu, Bochao and Lu, Chengda and Zhao, Chenggang and Deng, Chengqi and Zhang, Chenyu and Ruan, Chong and others},
  journal={arXiv preprint arXiv:2412.19437},
  year={2024}
}
@article{huatuogpt,
  title={HuatuoGPT-o1, towards medical complex reasoning with llms},
  author={Chen, Junying and Cai, Zhenyang and Ji, Ke and Wang, Xidong and Liu, Wanlong and Wang, Rongsheng and Hou, Jianye and Wang, Benyou},
  journal={arXiv preprint arXiv:2412.18925},
  year={2024}
}
@article{qwen2.5,
    title   = {Qwen2.5 Technical Report}, 
    author  = {An Yang and Baosong Yang and Beichen Zhang and Binyuan Hui and Bo Zheng and Bowen Yu and Chengyuan Li and Dayiheng Liu and Fei Huang and Haoran Wei and Huan Lin and Jian Yang and Jianhong Tu and Jianwei Zhang and Jianxin Yang and Jiaxi Yang and Jingren Zhou and Junyang Lin and Kai Dang and Keming Lu and Keqin Bao and Kexin Yang and Le Yu and Mei Li and Mingfeng Xue and Pei Zhang and Qin Zhu and Rui Men and Runji Lin and Tianhao Li and Tingyu Xia and Xingzhang Ren and Xuancheng Ren and Yang Fan and Yang Su and Yichang Zhang and Yu Wan and Yuqiong Liu and Zeyu Cui and Zhenru Zhang and Zihan Qiu},
    journal = {arXiv preprint arXiv:2412.15115},
    year    = {2024}
}
@article{Citrus,
  title={Citrus: Leveraging Expert Cognitive Pathways in a Medical Language Model for Advanced Medical Decision Support},
  author={Wang, Guoxin and Gao, Minyu and Yang, Shuai and Zhang, Ya and He, Lizhi and Huang, Liang and Xiao, Hanlin and Zhang, Yexuan and Li, Wanyue and Chen, Lu and others},
  journal={arXiv preprint arXiv:2502.18274},
  year={2025}
}
@misc{OpenBioLLMs,
  author = {Ankit Pal, Malaikannan Sankarasubbu},
  title = {OpenBioLLMs: Advancing Open-Source Large Language Models for Healthcare and Life Sciences},
  year = {2024},
  publisher = {Hugging Face},
  journal = {Hugging Face repository},
  howpublished = {\url{https://huggingface.co/aaditya/OpenBioLLM-Llama3-70B}}
}
@inproceedings{ultramedical,
 author = {Zhang, Kaiyan and Zeng, Sihang and Hua, Ermo and Ding, Ning and Chen, Zhang-Ren and Ma, Zhiyuan and Li, Haoxin and Cui, Ganqu and Qi, Biqing and Zhu, Xuekai and Lv, Xingtai and Hu, Jin-Fang and Liu, Zhiyuan and Zhou, Bowen},
 booktitle = {NeurIPS},
 title = {UltraMedical: Building Specialized Generalists in Biomedicine},
 year = {2024}
}
@article{yi,
  title={Yi: Open foundation models by 01. ai},
  author={Young, Alex and Chen, Bei and Li, Chao and Huang, Chengen and Zhang, Ge and Zhang, Guanwei and Wang, Guoyin and Li, Heng and Zhu, Jiangcheng and Chen, Jianqun and others},
  journal={arXiv preprint arXiv:2403.04652},
  year={2024}
}
@misc{qwq,
    title = {QwQ-32B: Embracing the Power of Reinforcement Learning},
    url = {https://qwenlm.github.io/zh/blog/qwq-32b/},
    author = {Qwen},
    month = {March},
    year = {2025}
}
@article{Gemma,
  title={Gemma 2: Improving open language models at a practical size},
  author={Team, Gemma and Riviere, Morgane and Pathak, Shreya and Sessa, Pier Giuseppe and Hardin, Cassidy and Bhupatiraju, Surya and Hussenot, L{\'e}onard and Mesnard, Thomas and Shahriari, Bobak and Ram{\'e}, Alexandre and others},
  journal={arXiv preprint arXiv:2408.00118},
  year={2024}
}
@article{phi,
  title={Phi-4 technical report},
  author={Abdin, Marah and Aneja, Jyoti and Behl, Harkirat and Bubeck, S{\'e}bastien and Eldan, Ronen and Gunasekar, Suriya and Harrison, Michael and Hewett, Russell J and Javaheripi, Mojan and Kauffmann, Piero and others},
  journal={arXiv preprint arXiv:2412.08905},
  year={2024}
}
@article{baichuan2023baichuan2,
  title={Baichuan 2: Open Large-scale Language Models},
  author={Baichuan},
  journal={arXiv preprint arXiv:2309.10305},
  url={https://arxiv.org/abs/2309.10305},
  year={2023}
}
@article{chatglm,
  title={ChatGLM: A family of large language models from glm-130b to glm-4 all tools},
  author={GLM, Team and Zeng, Aohan and Xu, Bin and Wang, Bowen and Zhang, Chenhui and Yin, Da and Zhang, Dan and Rojas, Diego and Feng, Guanyu and Zhao, Hanlin and others},
  journal={arXiv preprint arXiv:2406.12793},
  year={2024}
}
@article{internlm2,
  title={Internlm2 technical report},
  author={Cai, Zheng and Cao, Maosong and Chen, Haojiong and Chen, Kai and Chen, Keyu and Chen, Xin and Chen, Xun and Chen, Zehui and Chen, Zhi and Chu, Pei and others},
  journal={arXiv preprint arXiv:2403.17297},
  year={2024}
}
@misc{Bio-Medical-Llama-3-8B, author = "ContactDoctor", title = {Bio-Medical: A High-Performance Biomedical Language Model}, year = {2024}, howpublished = {https://huggingface.co/ContactDoctor/Bio-Medical-Llama-3-8B}, }
@article{finemedlm,
  title={FineMedLM-o1: Enhancing the medical reasoning ability of llm from supervised fine-tuning to test-time training},
  author={Yu, Hongzhou and Cheng, Tianhao and Cheng, Ying and Feng, Rui},
  journal={arXiv preprint arXiv:2501.09213},
  year={2025}
}
@misc{labrak2024biomistral,
  title={Biomistral: A collection of open-source pretrained large language models for medical domains},
  author={Labrak, Yanis and Bazoge, Adrien and Morin, Emmanuel and Gourraud, Pierre-Antoine and Rouvier, Mickael and Dufour, Richard},
  journal={arXiv preprint arXiv:2402.10373},
  year={2024}
}
@inproceedings{
  fineweb,
 author = {Penedo, Guilherme and Kydl\'{\i}\v{c}ek, Hynek and allal, Loubna Ben and Lozhkov, Anton and Mitchell, Margaret and Raffel, Colin and Von Werra, Leandro and Wolf, Thomas},
 booktitle = {NeurIPS},
 title = {The FineWeb Datasets: Decanting the web for the finest text data at scale},
 year = {2024}
}
@article{multilingualism,
  title={How do large language models handle multilingualism?},
  author={Zhao, Yiran and Zhang, Wenxuan and Chen, Guizhen and Kawaguchi, Kenji and Bing, Lidong},
  journal={arXiv preprint arXiv:2402.18815},
  year={2024}
}
@inproceedings{yao2023tree,
 author = {Yao, Shunyu and Yu, Dian and Zhao, Jeffrey and Shafran, Izhak and Griffiths, Tom and Cao, Yuan and Narasimhan, Karthik},
 booktitle = {NeurIPS},
 title = {Tree of Thoughts: Deliberate Problem Solving with Large Language Models},
 year = {2023}
}
@article{rise,
  title={The Rise and Down of Babel Tower: Investigating the Evolution Process of Multilingual Code Large Language Model},
  author={Chen, Jiawei and Chen, Wentao and Su, Jing and Xu, Jingjing and Lin, Hongyu and Ren, Mengjie and Lu, Yaojie and Han, Xianpei and Sun, Le},
  journal={arXiv preprint arXiv:2412.07298},
  year={2024}
}
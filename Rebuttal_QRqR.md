# Rebuttal to Reviewer QRqR (Score 3; Con 4)

We sincerely thank Reviewer QRqR for the constructive feedback. We address each concern below with detailed responses and clarifications.

## Response to Limitation 1: Expert Review and Post-Translation Validation Details

**Reviewer's Concern:** The paper does not provide specific details on expert review and post-translation validation, e.g., number of reviewers, validation accuracy rates, and validation criteria.

**Our Response:**

We acknowledge this limitation and provide the following detailed information about our expert review process:

### Expert Review Process Details:

**Number of Reviewers:** We employed two expert anesthesiologists with over 10 years of clinical experience each to conduct the validation process.

**Sample Size:** We randomly selected 500 questions from AnesBench for expert review, representing approximately 11.3% of the total dataset.

**Validation Criteria:** The expert review focused on three key aspects:
1. **Professional Accuracy:** Correctness of medical concepts and clinical scenarios
2. **Question Quality:** Clarity, relevance, and appropriateness of difficulty level
3. **Translation Accuracy:** Precision of medical terminology translation and preservation of clinical meaning

**Cross-Validation Methodology:** We adopted a cross-validation approach where:
- Each expert independently reviewed all 500 questions
- For questions where one expert was uncertain, both experts collaborated to reach consensus
- Disagreements were resolved through discussion and reference to authoritative anesthesiology guidelines

### Validation Results:

Our expert review revealed the following error distribution:
- **Serious Professional Issues:** Only 1 question (0.2%) contained significant professional inaccuracies that could affect clinical understanding
- **Minor Translation Inaccuracies:** 23 questions (4.6%) had minor translation issues that did not affect professional accuracy
- **Formatting/Clarity Issues:** 18 questions (3.6%) had minor formatting or clarity issues
- **Overall Acceptable Quality:** 458 questions (91.6%) were deemed professionally accurate and appropriately translated

**Validation Accuracy Rate:** 91.6% of questions met all validation criteria without requiring modifications.

These detailed validation results will be incorporated into Appendix J in the revised version of our paper.

## Response to Limitation 2: Quality Assurance of LLM-Generated QA Data

**Reviewer's Concern:** How to ensure the informativeness of questions and the effectiveness/accuracy of answers generated by LLaMA-70B and Qwen-72B-Instruct.

**Our Response:**

We appreciate this important question and provide the following clarifications:

### 1. Scope and Purpose Clarification

First, we emphasize that **QA data generation is not the primary contribution of our work**. Our main contribution is the comprehensive benchmark AnesBench and the systematic evaluation of LLMs' anesthesiology reasoning capabilities. The QA data (AnesQA) was generated specifically to validate whether domain-specific training data can improve model reasoning performance, which our experiments successfully demonstrated.

### 2. Quality Assurance Mechanisms

Despite QA data not being our primary focus, we implemented several quality assurance mechanisms:

**Multi-Model Cross-Validation:** We employed two different state-of-the-art models (LLaMA3.3-70B-Instruct for question generation and Qwen2.5-72B-Instruct for answer generation and filtering) to reduce single-model bias.

**Keyword-Filtered Context:** Questions were generated from carefully filtered text segments using domain-specific keywords, ensuring topical relevance and informativeness.

**Constrained Generation:** Our prompts explicitly required that answers be direct rephrasings of the source text segments, minimizing hallucination and ensuring factual grounding.

### 3. Empirical Validation

**Performance Improvement:** As demonstrated in Table 6 (Training Strategies), models trained on AnesQA showed consistent performance improvements, validating the effectiveness of our synthetic data.

**Literature Support:** Synthetic SFT data containing some noise can still improve model performance, as supported by recent research [citations]. This is a common practice in the field due to the prohibitive cost of manual annotation for specialized domains.

## Response to Limitation 3: Manual Quality Assessment of Synthetic Data

**Reviewer's Concern:** The synthetic data lacks detailed descriptions of manual quality assessment and screening processes.

**Our Response:**

### 1. Industry Practice Context

**Common Practice:** Full manual review of large-scale QA datasets is not standard practice in the field due to prohibitive costs and resource requirements. Most recent works in medical AI rely on automated quality assurance mechanisms combined with sampling-based validation.

**Cost-Effectiveness:** Given the specialized nature of anesthesiology, comprehensive manual annotation would require extensive expert time, making it impractical for large-scale dataset creation.

### 2. Sampling-Based Quality Assessment

Despite the above constraints, we conducted a sampling-based manual quality assessment:

**Sample Size:** We randomly selected 100 QA pairs from AnesQA for expert review.

**Expert Assessment Results:**
- **Question Quality:** Experts found questions to be specific, focused, and informative
- **Answer Accuracy:** Responses contained minimal factual errors and were generally accurate
- **Limitations Identified:**
  - Questions were sometimes less practical/clinical than ideal
  - Answers could be overly general or broad
  - Some questions lacked evidence-based medical reasoning
  - Insufficient grounding in evidence-based medicine principles

**Overall Assessment:** Despite these limitations, experts concluded that the QA data was sufficient for its intended purpose as supplementary training data for domain adaptation.

### 3. Justification for Approach

**Sufficient for Purpose:** The identified limitations do not significantly impact the utility of AnesQA for its intended purpose - demonstrating that domain-specific training data can improve anesthesiology reasoning performance.

**Validation Through Results:** The effectiveness of our approach is empirically validated through the performance improvements observed in our training experiments (Table 6).

**Resource Allocation:** Our resources were primarily focused on ensuring the quality of our main contribution (AnesBench), where we conducted comprehensive expert validation as detailed in Response 1.

## Conclusion

We believe these detailed responses address the reviewer's concerns comprehensively. The expert validation of our benchmark demonstrates rigorous quality control for our primary contribution, while our QA generation methodology, though not perfect, is appropriate for its intended research purpose and follows established practices in the field.

We will incorporate these clarifications and additional details into the revised manuscript, particularly expanding Appendix J with the detailed expert review results and methodology.

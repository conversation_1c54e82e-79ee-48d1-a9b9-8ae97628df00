# Rebuttal to Reviewer QRqR (Score 3; Con 4)

We sincerely thank **Reviewer QRqR** for the constructive feedback. We address each concern below with detailed responses and clarifications.

---

## **Response to Limitation 1: Expert Review and Post-Translation Validation Details**

> **Reviewer's Concern:** The paper does not provide specific details on expert review and post-translation validation, e.g., number of reviewers, validation accuracy rates, and validation criteria.

We acknowledge the reviewer's concern regarding the lack of specific details on expert review and post-translation validation, including the number of reviewers, validation accuracy rates, and validation criteria. We provide the following comprehensive information about our expert review process.

### **Expert Review Process**

Our validation process employed **two expert anesthesiologists**, each with **over 10 years of clinical experience**, to conduct thorough quality assessment. We randomly selected **500 questions** from AnesBench for expert review, representing approximately **11.3%** of the total dataset. This sample size was chosen to provide statistically meaningful insights while maintaining feasibility given the specialized expertise required.

The expert review focused on **three critical aspects** of question quality:

1. **Professional Accuracy:** Examining the correctness of medical concepts and clinical scenarios presented in each question
2. **Question Quality:** Including clarity, relevance, and appropriateness of difficulty level for the intended evaluation purpose
3. **Translation Accuracy:** Ensuring precision of medical terminology translation and preservation of clinical meaning across languages

### **Cross-Validation Methodology**

Our methodology employed a rigorous cross-validation approach to maximize reliability. Each expert independently reviewed all 500 questions to minimize bias and ensure comprehensive coverage. For questions where one expert expressed uncertainty, both experts collaborated to reach consensus through detailed discussion. Any disagreements were systematically resolved through reference to authoritative anesthesiology guidelines and established clinical standards.

### **Validation Results**

The validation results demonstrated **high overall quality** across our benchmark:

- **Serious Professional Issues:** Only **1 question (0.2%)** contained significant professional inaccuracies that could affect clinical understanding
- **Minor Translation Inaccuracies:** **23 questions (4.6%)** had minor translation issues that did not compromise professional accuracy
- **Formatting/Clarity Issues:** **18 questions (3.6%)** presented minor formatting or clarity issues that could be easily addressed
- **Overall Acceptable Quality:** **458 questions (91.6%)** were deemed professionally accurate and appropriately translated, meeting all validation criteria without requiring modifications

The overall **validation accuracy rate of 91.6%** demonstrates the high quality of our benchmark construction process. These detailed validation results will be incorporated into **Appendix J** in the revised version of our paper to provide complete transparency regarding our quality assurance procedures.

---

## **Response to Limitation 2: Quality Assurance of LLM-Generated QA Data**

> **Reviewer's Concern:** How to ensure the informativeness of questions and the effectiveness/accuracy of answers generated by LLaMA-70B and Qwen-72B-Instruct.

We appreciate the reviewer's important question regarding how to ensure the informativeness of questions and the effectiveness/accuracy of answers generated by LLaMA-70B and Qwen-72B-Instruct. We provide the following comprehensive clarifications to address this concern.

### **Scope and Purpose Clarification**

Firstly, we need to emphasize that **QA data generation is not the primary contribution of our work**. Our main contribution is the comprehensive benchmark AnesBench and the systematic evaluation of LLMs' anesthesiology reasoning capabilities. The QA data (AnesQA) was generated specifically to validate whether domain-specific training data can improve model reasoning performance, which our experiments successfully demonstrated. This distinction is important for understanding the scope and purpose of our synthetic data generation efforts.

### **Quality Assurance Mechanisms**

Despite QA data not being our primary focus, we implemented several **robust quality assurance mechanisms** to ensure data reliability:

- **Multi-Model Cross-Validation:** We employed two different state-of-the-art models (LLaMA3.3-70B-Instruct for question generation and Qwen2.5-72B-Instruct for answer generation and filtering) to reduce single-model bias and improve overall quality
- **Keyword-Filtered Context:** Questions were generated from carefully filtered text segments using domain-specific keywords, ensuring topical relevance and informativeness
- **Constrained Generation:** Our approach explicitly required that answers be direct rephrasings of the source text segments, minimizing hallucination and ensuring factual grounding in the original medical literature

### **Empirical Validation**

The effectiveness of our approach is supported by empirical validation. As demonstrated in **Table 6 (Training Strategies)**, models trained on AnesQA showed **consistent performance improvements**, validating the effectiveness of our synthetic data for its intended purpose. Furthermore, this approach aligns with established practices in the field, as synthetic SFT data containing some noise can still improve model performance, supported by recent research [1,2,3]. This methodology represents a *common practice* in specialized domains due to the prohibitive cost of manual annotation for highly technical medical content [4,5,6].

---

## **Response to Limitation 3: Manual Quality Assessment of Synthetic Data**

> **Reviewer's Concern:** The synthetic data lacks detailed descriptions of manual quality assessment and screening processes.

We understand the reviewer's concern regarding the synthetic data lacking detailed descriptions of manual quality assessment and screening processes. We provide the following comprehensive response addressing this limitation.

### **Industry Practice Context**

It is important to contextualize our approach within current industry practices. **Full manual review of large-scale QA datasets is not standard practice** in the field due to prohibitive costs and resource requirements [5,6]. Most recent works in medical AI rely on automated quality assurance mechanisms, which represents the current state-of-the-art approach. Given the specialized nature of anesthesiology, comprehensive manual annotation would require extensive expert time from qualified anesthesiologists, making it impractical for large-scale dataset creation while maintaining reasonable research timelines and resource constraints.

### **Sampling-Based Quality Assessment**

Despite these constraints, we conducted a thorough **sampling-based manual quality assessment** to ensure data reliability. We randomly selected **100 QA pairs** from AnesQA for expert review, providing a representative sample for quality evaluation.

**Expert Assessment Results:**

- **Positive Findings:** Experts found questions to be *specific, focused, and informative*, meeting the basic requirements for training data. Responses contained *minimal factual errors* and were generally accurate in their medical content
- **Identified Limitations:** Questions were sometimes less practical or clinical than would be ideal for real-world application. Answers could occasionally be overly general or broad, lacking the specificity that might be preferred in clinical contexts. Some questions lacked evidence-based medical reasoning, and there was insufficient grounding in evidence-based medicine principles in certain cases

### **Justification and Validation**

Despite these identified limitations, experts concluded that **the QA data was sufficient for its intended purpose** as supplementary training data for domain adaptation. The identified limitations do not significantly impact the utility of AnesQA for its intended purpose, which is demonstrating that domain-specific training data can improve anesthesiology reasoning performance.

The effectiveness of our approach is **empirically validated** through the performance improvements observed in our training experiments, as shown in **Table 6**. Furthermore, our resource allocation strategy prioritized ensuring the quality of our main contribution (AnesBench), where we conducted comprehensive expert validation as detailed in our response to Limitation 1, while applying appropriate quality assurance measures to the supplementary training data.

---

## **Conclusion**

We believe these detailed responses **comprehensively address the reviewer's concerns** and provide the transparency requested regarding our methodology and quality assurance processes. The expert validation of our benchmark demonstrates **rigorous quality control** for our primary contribution, establishing AnesBench as a reliable evaluation tool for anesthesiology reasoning in large language models. While our QA generation methodology is not perfect, it is *appropriate for its intended research purpose* and follows established practices in the field for synthetic data generation in specialized medical domains.

We will incorporate these clarifications and additional details into the revised manuscript, particularly **expanding Appendix J** with the detailed expert review results and methodology. This will ensure that future readers have complete access to our quality assurance procedures and can fully understand the rigor applied to both our benchmark construction and supplementary data generation processes.

---

## **References**

[1] Puri, R., Spring, R., Patwary, M., Shoeybi, M., & Catanzaro, B. (2020). Training question answering models from synthetic data. *arXiv preprint arXiv:2002.09599*.

[2] Shakeri, S., Santos, C. N. D., Zhu, H., Ng, P., Nan, F., Wang, Z., ... & Xiang, B. (2020). End-to-end synthetic data generation for domain adaptation of question answering systems. *arXiv preprint arXiv:2010.06028*.

[3] Bai, F., Harrigian, K., Stremmel, J., Hassanzadeh, H., Saeedi, A., & Dredze, M. (2024). Give me Some Hard Questions: Synthetic Data Generation for Clinical QA. *arXiv preprint arXiv:2412.04573*.

[4] Zhang, H., Zeng, P., Hu, Y., Qian, J., Song, J., & Gao, L. (2023). Learning visual question answering on controlled semantic noisy labels. *Pattern Recognition*, 138, 109339.

[5] Chen, R. J., Lu, M. Y., Chen, T. Y., Williamson, D. F., & Mahmood, F. (2021). Synthetic data in machine learning for medicine and healthcare. *Nature Biomedical Engineering*, 5(6), 493-497.

[6] Alismail, A., & Lanquillon, C. (2025, May). A Survey of LLM-Based Methods for Synthetic Data Generation and the Rise of Agentic Workflows. In *International Conference on Human-Computer Interaction* (pp. 119-135). Cham: Springer Nature Switzerland.
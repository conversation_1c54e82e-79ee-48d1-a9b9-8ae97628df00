\begin{figure}[h]
  \centering
  \includegraphics[width=0.98\textwidth]{figs/overview.pdf}
  \caption{\textbf{Overview of our work and proposed benchmark} The top section of the figure presents the benchmark’s basic information, including examples for three question types and statistics. The bottom section displays multiple dimensions that may be related to the model’s reasoning ability, as revealed by our analyses and experiments.}
\label{fig:overview}
\end{figure}


\section{Introduction}
\label{sec:intro}

% The success of large language models (LLMs) has fundamentally transformed the field of medical artificial intelligence and spurred the development of specialized medical models. Enhancing the reasoning capabilities of specialized medical LLMs remains an unresolved issue. The reasoning capabilities of LLMs in the medical field still require substantial advancement, especially when compared to general reasoning tasks such as mathematics and programming~\cite{medicalLagsBehind}.

% However, the enhancement of reasoning capabilities in specialized medical LLMs continues to pose a challenge

The success of LLMs has fundamentally reshaped the domain of medical artificial intelligence and catalyzed the creation of specialized medical models~\cite{LLMSuccess}. However, the reasoning abilities of LLMs within the medical sector still need significant improvement, particularly when compared with general reasoning tasks such as mathematics and programming~\cite{medicalLagsBehind}.

Anesthesiology, as a highly specialized discipline in medicine, requires extensive medical knowledge and high-risk decision-making. General medical Question-Answering (QA) primarily involves fact retrieval (System 1), whereas anesthesia decision-making demands complex, context-aware judgments and reasoning ability (System 2)~\cite{Fast&SlowThinking}. Existing benchmarks overlook the unique characteristics of anesthesiology or limit evaluations to general fact retrieval~\cite{AnesBenchmark_explicit_1,AnesBenchmark_explicit_2,CMB,AnesBenchmark_implicit_1,AnesBenchmark_implicit_2,AnesBenchmark_implicit_3,AnesBenchmark_implicit_4,CAB}. Our study aims to systematically assess LLMs' anesthesia reasoning capabilities and investigate the factors that influence these abilities.

To this end, we construct \anesbench, a comprehensive cross-lingual benchmark designed to evaluate both fact retrieval and decision-making in anesthesia-related tasks. \anesbench categorizes reasoning tasks into three levels. System 1 focuses on fact retrieval. System 2 involves complex reasoning and decision-making. System 1.x represents a hybrid category that requires both factual recall and basic reasoning~\cite{System1.x}. Utilizing \anesbench, we conduct multiple experiments and carry out a detailed and multi-dimensional analysis of the anesthesiology reasoning capabilities of representative LLMs, including those in general and medical domains.

%  我们利用\anesbench和多个实验对LLMs的麻醉学推理能力进行了详细和多维度的分析。

Currently, analyses of factors enhancing LLM reasoning capabilities focus on two main issues. The first issue focuses on identifying the characteristics typically exhibited by LLMs with enhanced reasoning capabilities. The second issue concerns how to endow LLMs with enhanced reasoning skills. In the context of anesthesiology reasoning, our analysis of the first issue considers several fundamental model characteristics, including model scale, CoT~\cite{Zero-Shot-COT} length, and language transferability~\cite{language_trans}. Our analysis of the second issue examines several training and reasoning methodologies, encompassing CPT ~\cite{CPT}, SFT ~\cite{SFT}, knowledge distillation~\cite{knowledge_dis}, and test-time reasoning techniques~\cite{self-consistency,beamSearch,MCTS_1}. 
% Additionally, to ensure the reliability of our analysis, we employ a data leakage detection method specifically designed for multiple-choice benchmarks to analyze potential data leakage~\cite{DataLeakageDetect}.

% 对于第一个issue，我们得到了以下几个关键见解：For the first issue, we obtained the following key insights:

% Based on an analysis of performance across over thirty state-of-the-art LLMs, we derived the following key insights for the first issue. 

% Larger model scales improve performance on System 1 tasks by expanding the knowledge base. However, there is diminishing marginal utility. In addition, gains are less pronounced for System 2 reasoning, highlighting that merely increasing model size is insufficient to enhance reasoning capabilities.


For the first issue, we analyze reasoning performance across over thirty state-of-the-art LLMs and obtain the following key insights: (1) System 1 tasks benefit from larger model scales by expanding their knowledge base, while System 2 reasoning exhibits comparatively smaller gains. Notably, both systems experience diminishing marginal returns, with each additional unit of scale contributing progressively less to performance improvements. (2) CoT length plays an important role in enhancing performance, particularly in System 2 tasks, by enabling more structured and step-by-step reasoning. (3) Language transferability is still an important factor limiting the performance of multilingual models. We suggest supplementing bilingual domain-specific data in the CPT stage to alleviate the language gap. 

% Language transferability仍然是限制bilingual models表现的重要因素。我们认为这种限制可能与预训练数据中语言分布有关。

% 我们建议在CPT阶段补充双语的domain specific数据用于缓解语言间gap。

% Language transferability affects the performance of bilingual models. 

% Multilingual models exhibit lower efficiency on System 2 tasks compared to models trained in a single language, potentially indicating that language diversity influences higher-order reasoning.
% 相较于general domain，在specific domain，多语言支持反而可能成为限制语言迁移性的因素。 在llama3的报告中，虽然在MGSM以及Multilingual  MMLU上有更高的

% Language transferability仍然是影响bilingual models表现的重要因素。根据~\cite{}的理论，LLM的语言学习进入稳定阶段后不同语言会形成独立的知识体系而不再依赖于翻译。但是如果此时，语言独立知识体系中的domain specific知识不足可能就会产生巨大的语言性能差异。 因此，我们建议在CPT阶段补充双语的领域专业知识。

% To further understand the impact of training and reasoning strategies on anesthesia reasoning, we conducted extensive evaluations. 

% 之后，我们使用两阶段的训练策略进行多组实验，并且使用多种reasoning techniques进行推理实验。

% Additionally, we conduct multiple experiments using a two-stage training strategy and perform evaluation using various reasoning techniques.

% conduct extensive experiments to further understand how training and reasoning strategies affect anesthesia reasoning. 

For the second issue, we investigate how training and reasoning strategies affect anesthesia reasoning. We first construct CPT and SFT datasets for the anesthesiology domain and conduct multiple experiments using a two-stage training strategy. Then, we implement and analyze various test-time compute reasoning techniques, including Best-of-N~\cite{yao2023tree} and Beam Search~\cite{beamSearch}. Furthermore, we evaluate LLMs enhanced by DeepSeek-R1 distillation. Our findings indicate that SFT alone is insufficient to enhance System 2 reasoning, even though it improves System 1 performance. On the other hand, test-time compute strategies consistently boost reasoning performance. Additionally, our experiments reveal that larger models benefit more from distillation of reasoning data than small LLMs when addressing System 2 anesthesia problems. 
% These techniques proved essential for improving reasoning accuracy and decision reliability in complex tasks, underscoring the importance of reasoning strategies in dynamic, high-risk medical scenarios such as anesthesia.

Our contribution can be summarized as follows:
\begin{itemize}
    % \item We created a bilingual evaluation framework featuring a three-tier reasoning system (System 1, System 1.x, and System 2). This framework systematically distinguishes between knowledge retrieval and reasoning capability assessments, filling the evaluation gap in Anesthesiology.
    \item We introduce a cross-lingual benchmark with a three-level reasoning system (System 1, System 1.x, and System 2) that systematically disentangles knowledge retrieval from reasoning capabilities, bridging a critical gap in anesthesiology evaluation.
    \item Based on extensive evaluation results, our work demonstrates the impact of several model characteristics, such as model scale, language transferability, and COT length, in relation to anesthesiology reasoning performance, yielding several key insights.
    \item Using a curated dataset, our extensive experiments demonstrate the effectiveness of CPT and SFT training strategies while evaluating reasoning techniques such as Best-of-N sampling and beam search. Furthermore, we investigate the impact of distillation on anesthesiology reasoning, using an analysis of DeepSeek-R1 distilled models.

    % 此外，我们使用DeepSeek-R1 distilled models，调查蒸馏对麻醉推理能力的影响。
    % \item In-depth analyses and experiments revealed several core model features that influence anesthesia reasoning. Our work also validated various reasoning-enhancing training methodologies and inference paradigms.
    % \item Concurrently, we constructed CPT and SFT datasets for training anesthesia-specific LLMs, which can effectively improve model performance in xxxx.
\end{itemize}

% 在广泛的实验结果上，我们深入分析数个model characteristic，如model scale，language transferability和COT长度，与麻醉学推理性能的关系，并得到了多个关键见解。

% 通过多组训练以及evaluation实验，使用我们curate的数据集，我们验证了CPT,SFT训练策略的影响，并调查推理techniques，如best of N和beam search。最后，我们分析了DeepSeek-R1蒸馏模型的表现。

% 模型特征方面，我们的发现包括：（1） model scale可以提供边际效用递减的性能提升。并且提升在system1和system1.x问题上相较于system2上更为显著。 （2） 对于system2类型问题，更长的COT长度往往有更高的表现。 （3） 在麻醉学推理问题上，即使是双语模型，仍然可能在语言转换时存在巨大的性能gap。

Overall, we are committed to leveraging our benchmark and in-depth analyses to provide valuable insights for developing LLMs with enhanced anesthesiology reasoning capabilities. We will publicly release our benchmark, the CPT and SFT datasets, and the evaluation code.
% 1. 针对现有基准测试忽视麻醉学复杂决策特性的问题，创建了包含三级推理体系（系统1、系统1.x、系统2）的双语评估框架，系统性区分知识检索与高风险决策能力评估，填补了专业医疗LLM在动态临床场景评测的空白。
% 2. 通过深入的分析与实验，揭示了影响麻醉推理的数个核心模型特征，同时验证了不同的推理增强训练方法与inference范式。
% 3. 在分析和实验的同时，我们构建了一个可以用于训练麻醉专用LLM的CPT和SFT数据集，可以有效提高模型在xxxx方面的表现。

% 总的来说，我们希望通过我们的benchmark以及深入的分析可以为开发麻醉学推理能力更强大的专用LLM提供有价值的见解。我们会公开release我们所构建的benchmark，CPT,SFT数据集以及评测代码。


% 大型语言模型(LLM)的成功彻底改变了医学人工智能领域，导致了医学专用模型的发展。但是，如何提高大语言模型的推理能力仍然是悬而未决的。尤其是在通用医学及其子学科，尽管有很多工作致力于提升医学专用模型的推理能力，由于结果的难以验证性的特性，该领域的发展仍然严重落后于数学或者编程等通用的推理任务。 

% 而麻醉作为医学中高度专业化的学科，不仅需要广泛的医学知识，还需要高风险的决策。与主要涉及事实检索(系统1任务)的一般医学QA不同，麻醉决策需要复杂的、上下文感知的判断(系统2任务)。现有的Benchmark往往忽略了麻醉学科的特殊性，或者评估仅限于一般的麻醉学科事实检索。为了弥合这个gap并开发具有更强麻醉学科推理能力的LLM提供insight， 我们旨在系统地评估LLMs的麻醉推理能力并调查影响麻醉学科推理能力的因素。

% 为此，we construct \anesbench, a comprehensive 双语 benchmark designed to evaluate both knowledge retrieval and decision making in tasks related to anesthesia.  具体地，\anesbench 将推理任务分为三个层次进行结构化评估:(1)系统1，侧重于知识检索；(2)系统2，涉及复杂的推理和决策；(3) System 1.x，一个既需要事实回忆又需要基本推理的混合类别。

% 当前，分析提升LLM推理能力的因素时往往会考虑两个主要问题。第一，什么样的LLM可能会具有更强的推理能力。第二，如何让LLM具有更强的推理能力。在麻醉学推理语境下，我们针对第一个问题主要考虑了包括模型的数个基本特征，包括模型规模、CoT推理长度和语言迁移性。针对第二个问题，我们分析了数个训练和推理方法论，encompassing continuous pretraining (CPT), supervised fine-tuning (SFT), knowledge distillation, and test-time reasoning techniques. 除此之外，为了保证我们分析结果的可靠性，我们同时使用了一种专门为多项选择题benchmark设计的数据泄露检测方法用于对可能的数据泄露进行分析。

% 最后, 根据在超过三十个SOTA LLMs上表现的分析，我们针对第一个问题，得到了如下的关键见解：（1） 更大的模型比例通过扩大知识库来提高系统1任务的性能。但是对于系统2推理，好处不太显著，突出了增强模型推理能力单纯增大model scale是不够的。（2） 此外，CoT长度在提高绩效方面起着至关重要的作用，特别是在系统2任务中，通过实现更结构化和逐步推理。 （3） 语言迁移性影响双语模型的表现，与接受单一语言训练的模型相比，多语模型在系统2任务中表现出较低的效率，这表明语言多样性影响高阶推理。

% 然后，为了进一步了解训练和推理策略对麻醉推理的影响，我们进行了几次广泛的评估。 首先，我们为麻醉学领域构建了CPT和SFT数据集。我们的发现表明，SFT单独不足以增强需要复杂决策的系统2推理，尽管它提高了系统1的性能。 此外，我们评估了DeepSeek蒸馏增强的小LLM，表明在麻醉学system2类型问题上，scale更大的模型可以从增强推理模型的蒸馏中获得相较于小LLM更大的收益。 最后，我们实施并探索了几种测试时推理技术，包括自洽性、波束搜索和蒙特卡罗树搜索(MCTS)，所有这些技术都证明对于提高复杂任务中的推理准确性和决策可靠性至关重要，强调了推理时策略对于动态、高风险医疗场景(如麻醉)的重要性。

%我们首先评估模型特征对麻醉推理的影响，并得出关键的见解。更大的模型比例通过扩大知识库来提高系统1任务的性能，但是对于系统2推理，好处不太显著，突出了不仅仅是比例的需要。此外，CoT长度在提高绩效方面起着至关重要的作用，特别是在系统2任务中，通过实现更结构化和逐步推理。最后，语言迁移性影响双语模型的表现，与接受单一语言训练的模型相比，多语模型在系统2任务中表现出较低的效率，这表明语言多样性影响高阶推理。

% 然后，为了进一步了解训练和推理策略对麻醉推理的影响，我们进行了几次广泛的评估。 首先，我们为麻醉学领域构建了CPT和SFT数据集。我们的发现表明，SFT单独不足以增强需要复杂决策的系统2推理，尽管它提高了系统1的性能。 然后，我们评估了DeepSeek提取的Qwen-base模型，表明从增强的推理模型中提取有效地提高了系统2的性能。 最后，我们实施并探索了几种测试时推理技术，包括自洽性、波束搜索和蒙特卡罗树搜索(MCTS)，所有这些技术都证明对于提高复杂任务中的推理准确性和决策可靠性至关重要，强调了推理时策略对于动态、高风险医疗场景(如麻醉)的重要性。


% % 包括主要验证了使用通用域上的推理数据蒸馏模型对麻醉学这样的domain specific问题的影响以及



% % 对于第一点，往往需要考虑模型本身的特征，常见的分析角度包括探索、规划和自我检查等输出结构和更长的推理长度等等


% % 这些模型通常会在医疗QA数据集上进行监督微调(SFT)，使它们能够生成类似人类的响应。虽然在一般医疗情况下有效，但它们在麻醉学等专业领域的适用性仍然有限。

% The advent of large language models (LLMs) has revolutionized the field of medical AI, leading to the development of specialized models for medical question-answering (QA). These models typically undergo supervised fine-tuning (SFT) on medical QA datasets, enabling them to generate human-like responses. While effective in general medical scenarios, their applicability to specialized fields like anesthesiology remains limited.

% % 麻醉是一门高度专业化的学科，不仅需要广泛的医学知识，还需要高风险的决策。与主要涉及事实检索(系统1任务)的一般医学QA不同，麻醉实践需要复杂的、上下文感知的判断(系统2任务)。现有的麻醉模型，如修普诺斯模型，主要是通过渐进SFT提高质量保证的性能，但未能解决临床麻醉所需的复杂推理过程。因此，虽然这些模型可以准确地响应直截了当的查询，但它们处理复杂麻醉场景的能力仍不清楚。

% Anesthesia is a highly specialized discipline requiring not only extensive medical knowledge but also high-stakes decision-making. Unlike general medical QA, which primarily involves factual retrieval (System 1 tasks), anesthetic practice demands complex, context-aware judgment (System 2 tasks). Existing anesthetic models, such as Hypnos~\cite{}, primarily enhance QA performance through progressive SFT but fail to address the intricate reasoning processes required in clinical anesthesia. Consequently, while these models may respond accurately to straightforward queries, their ability to handle complex anesthesia scenarios remains unclear.

% % 在本文中，我们旨在系统地评估LLMs的麻醉推理能力，并分析影响其性能的关键因素，为麻醉人工智能的未来发展提供见解。为了实现这一点，我们构建了一个全面的基准，旨在评估麻醉相关任务中的知识检索和决策制定。

% In this paper, we aim to systematically assess LLMs' anesthetic reasoning capabilities and analyze key factors influencing their performance, providing insights for future advancements in anesthetic AI. To achieve this, we construct \anesbench, a comprehensive benchmark designed to evaluate both knowledge retrieval and decision making in tasks related to anesthesia. 

% % \textbf{AnesBench}将推理任务分为三个层次进行结构化评估:(1)系统1，侧重于知识检索；(2)系统2，涉及复杂的推理和决策；(3) System 1.x，一个既需要事实回忆又需要基本推理的混合类别。

% % combine
% \textbf{AnesBench} categorizes reasoning tasks into three levels for structured assessment: (1) System 1, which focuses on knowledge retrieval; (2) System 2, which involves complex reasoning and decision-making; and (3) System 1.x, a hybrid category requiring both factual recall and basic reasoning.

% % 为了全面评估LLM在麻醉推理方面的表现，我们考察了影响推理能力的多个因素，分为两个关键维度:(1)模型特征，包括模型规模、CoT长度和语言迁移性；(2)训练和推理方法，包括连续预训练(CPT)、监督微调(SFT)、知识提炼和测试时推理技术。

% To comprehensively evaluate LLM performance on anesthetic reasoning, we examine multiple factors that influence reasoning abilities, organized into two key dimensions: (1) model characteristics, including model scale, CoT length, and language transferability; and (2) training and inference methodologies, encompassing continuous pretraining (CPT), supervised fine-tuning (SFT), knowledge distillation, and test-time reasoning techniques.

% We first evaluate the impact of model characteristics on anesthetic reasoning and derive key insights. 
% Larger model scale improves performance in System 1 tasks by broadening the knowledge base, but for System 2 reasoning, the benefits are less significant, highlighting the need for more than just scaling. 
% Additionally, CoT length plays a crucial role in enhancing performance, particularly in System 2 tasks, by enabling more structured and step-by-step reasoning. 
% Lastly, language transferability affects bilingual model performance, with multilingual models showing reduced effectiveness in System 2 tasks compared to those trained in a single language, suggesting that language diversity impacts higher-order reasoning.


% Then, to further understand the impact of training and reasoning strategies on anesthetic reasoning, we conducted several extensive evaluations. 
% First, we constructed CPT and SFT datasets for the anesthesiology domain. Our findings indicate that SFT alone is insufficient to enhance System 2 reasoning, which requires complex decision-making, though it improves System 1 performance. 
% We then assessed the DeepSeek-distilled Qwen-base model, showing that distillation from enhanced reasoning models effectively improves System 2 performance. 
% Finally, we implemented and explored several test-time reasoning techniques, including Self-Consistency, Beam Search, and Monte Carlo Tree Search (MCTS), all of which proved essential for improving reasoning accuracy and decision reliability in complex tasks, emphasizing the importance of inference-time strategies for dynamic, high-stakes medical scenarios like anesthesia.



% To comprehensively assess the performance of LLMs on AnesBench, we evaluate both intrinsic model factors, including model scale, CoT length, language transferability) and training & reasoning strategies, including CPT, SFT, knowledge distillation, inference-time reasoning techniques.


% We benchmark multiple LLM families, including LLaMA, Qwen, GPT, and DeepSeek, and observe that our dataset poses greater challenges than existing benchmarks, particularly in distinguishing System 1 from System 2 reasoning.

% Furthermore, our study quantifies the impact of different factors on anesthetic reasoning performance, including model scale, language, and CoT length, yielding the following key insights.


% Given these insights, we present a comprehensive evaluation of training and inference strategies within the anesthesia domain.

% The advent of large language models (LLMs) has revolutionized the field of medicine, leading to the development of numerous medical models designed for medical question-answering (QA). These models typically undergo supervised fine-tuning (SFT) with medical QA datasets on the pre-training models, enabling them to generate human-like responses. The goal is to equip these models with the capability to perform effectively in real-world medical applications.

% While these models have made significant strides in general medical domains, their application to specialized fields such as anesthesia remains limited. This is because anesthesia is a highly specialized field that demands not only extensive medical knowledge but also the ability to make complex, context-aware decisions. Unlike general medical QA, anesthesia involves dynamic, high-stakes decision-making processes that require reasoning beyond factual recall. Existing anesthetic models, such as Hypnos~\cite{}, focus on enhancing QA performance through progressive SFT but not consider the intricate reasoning and decision-making capabilities necessary in anesthetic practice. Consequently, while these models may provide accurate responses to straightforward questions, their ability to handle complex anesthesia scenarios remains unclear.


% In this paper, we aim to systematically benchmark the reasoning and decision-making capabilities of LLMs in anesthesiology. Specifically, we construct an English-language benchmark and combine it with an existing Chinese benchmark for evaluation.  To facilitate structured assessment, we categorize reasoning tasks into three levels: (1) System 1, which involves knowledge retrieval; (2) System 2, which requires complex reasoning and decision-making; and (3) System 1.x, a hybrid category necessitating both factual retrieval and basic reasoning.



% We perform benchmark on several LLM series, such as llama, qwen ......
% % 加一段数据集比较

% Based on this benchmark, we make several key observations regarding model performance. 
% (1) increasing model size improves performance across all task types, but the gains for System 2 (complex reasoning) are significantly lower than those for System 1 and System 1.x, indicating that model scaling alone is insufficient to enhance reasoning capabilities. 
% % 第二点主要是sft
% (2) 
% % SFT models tend to inherit the linguistic properties of their base models, as evidenced by the similar performance patterns observed in SFT models fine-tuned from Qwen or LLaMA on translated bilingual benchmarks. Furthermore, DeepSeek-R1-distilled models exhibit performance degradation, suggesting that SFT is not always an effective approach for enhancing reasoning ability. 
% SFT models are inherently constrained by their base models, highlighting the limitations of SFT in boosting reasoning performance. For instance, models fine-tuned from Qwen (e.g., Huatuo) and LLaMA (e.g., FineMed) exhibit similar patterns in bilingual benchmarks. Even models distilled from enhanced reasoning models, such as DeepSeek-R1, struggle to achieve significant improvements.
% % 拆分
% % not enough
% (3) models employing longer Chain-of-Thought (CoT) reasoning generally achieve better results, particularly in System 2 tasks, demonstrating the effectiveness of reasoning strategies at inference time.
% % 

% Given these insights, we present a comprehensive evaluation of training and inference strategies within the anesthesia domain. Since SFT alone does not necessarily improve reasoning, we construct a 4B-token pre-training dataset and a 2M-instance SFT dataset tailored for anesthesia-specific tasks, aiming to provide extensive domain knowledge while mitigating the limitations of direct SFT. Furthermore, the effectiveness of longer CoT reasoning suggests that test-time compute strategies play a crucial role in enhancing decision-making capabilities. Therefore, we explore inference-time techniques such as () to improve model performance in complex reasoning tasks. 

% These 

% We believe this benchmarking process will provide valuable insights to guide the future development of anesthesia-specific models.


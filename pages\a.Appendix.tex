\newpage
\appendix
\section{Complete Results}
\label{appendix:moreResult}

% 我们选取了当前被广泛使用的闭源模型GPT-4o和四十多个开源模型进行测评，包括最新的Llama4、Qwen3、Gemma3等系列模型。目前全部的测评结果如下表所示，部分模型由于资源限制尚未完成测试，后续将持续更新在项目主页。

% We select GPT-4o and \textbf{46} open source models for evaluation, including models from the latest series such as Llama4 and Qwen3. Results of more models will be updated on the project homepage.

\begin{table*}[h!]
\caption{\textbf{Complete Evaluation Results on \anesbench and AMCQA}.}
\setlength\extrarowheight{1.5pt}
\renewcommand{\arraystretch}{0.9}
\setlength{\tabcolsep}{2pt}
\resizebox{\textwidth}{!}{
\begin{tabular}{lp{1.5mm}rrrrp{1.5mm}rrrrp{1.5mm}c}
\toprule
\multirow{2}{*}{Model} && \multicolumn{4}{c}{\textbf{\anesbench}} && \multicolumn{4}{c}{\textbf{AMCQA}} && \multirow{2}{*}{\textbf{Avg.}} \\
\cline{3-6} \cline{8-11}
 && Sys1 & Sys1.x & Sys2 & Total && Sys1 & Sys1.x & Sys2 & Total && \\
 \midrule
 Qwen3-0.6B~\cite{qwen3}                        &  & 0.39     & 0.31        & 0.26     & 0.36      &  & 0.37     & 0.35        & 0.31     & 0.37      &  & 0.36       \\
gemma-3-1b-it~\cite{gemma3}                  &  & 0.33     & 0.26        & 0.21     & 0.30      &  & 0.26     & 0.25        & 0.21     & 0.26      &  & 0.28       \\
DeepSeek-R1-Distill-Qwen-1.5B~\cite{Deepseek-R1}  &  & 0.32     & 0.28        & 0.27     & 0.31      &  & 0.26     & 0.26        & 0.22     & 0.26      &  & 0.28       \\
Qwen3-1.7B ~\cite{qwen3}                   &  & 0.48     & 0.37        & 0.30     & 0.44      &  & 0.54     & 0.50        & 0.44     & 0.53      &  & 0.48       \\
Qwen3-4B    ~\cite{qwen3}                   &  & 0.60     & 0.46        & 0.34     & 0.54      &  & 0.54     & 0.48        & 0.48     & 0.53      &  & 0.53       \\
gemma-3-4b-it ~\cite{gemma3}                 &  & 0.46     & 0.36        & 0.35     & 0.42      &  & 0.43     & 0.41        & 0.37     & 0.43      &  & 0.42       \\
chatglm3-6b    ~\cite{chatglm}                &  & 0.37     & 0.28        & 0.25     & 0.34      &  & 0.36     & 0.36        & 0.34     & 0.36      &  & 0.35       \\
Qwen2.5-7B-Instruct  ~\cite{qwen2.5}          &  & 0.56     & 0.44        & 0.36     & 0.52      &  & 0.68     & 0.63        & 0.58     & 0.67      &  & 0.59       \\
HuatuoGPT-o1-7B   ~\cite{huatuogpt}             &  & 0.56     & 0.45        & 0.38     & 0.52      &  & 0.71     & 0.65        & 0.63     & 0.70      &  & 0.61       \\
Baichuan2-7B-Chat ~\cite{baichuan2023baichuan2}             &  & 0.39     & 0.31        & 0.30     & 0.37      &  & 0.44     & 0.41        & 0.41     & 0.43      &  & 0.40       \\
BioMistral-7B     ~\cite{labrak2024biomistral}             &  & 0.43     & 0.30        & 0.32     & 0.39      &  & 0.26     & 0.25        & 0.28     & 0.26      &  & 0.32       \\
DeepSeek-R1-Distill-Qwen-7B ~\cite{Deepseek-R1}   &  & 0.40     & 0.34        & 0.28     & 0.38      &  & 0.33     & 0.32        & 0.34     & 0.33      &  & 0.35       \\
Qwen3-8B        ~\cite{qwen3}               &  & 0.65     & 0.50        & 0.40     & 0.60      &  & 0.68     & 0.53        & 0.50     & 0.65      &  & 0.62       \\
Meta-Llama-3-8B-Instruct ~\cite{Llama3}      &  & 0.54     & 0.42        & 0.39     & 0.50      &  & 0.49     & 0.47        & 0.47     & 0.49      &  & 0.49       \\
Llama-3.1-8B-Instruct  ~\cite{Llama3}        &  & 0.58     & 0.45        & 0.36     & 0.53      &  & 0.53     & 0.53        & 0.55     & 0.53      &  & 0.53       \\
Llama-3.1-8B-UltraMedical ~\cite{Llama3}     &  & 0.63     & 0.47        & 0.41     & 0.57      &  & 0.54     & 0.52        & 0.50     & 0.54      &  & 0.56       \\
HuatuoGPT-o1-8B ~\cite{huatuogpt}               &  & 0.58     & 0.46        & 0.39     & 0.53      &  & 0.57     & 0.53        & 0.57     & 0.56      &  & 0.55       \\
Llama3-OpenBioLLM-8B  ~\cite{OpenBioLLMs}         &  & 0.44     & 0.35        & 0.30     & 0.41      &  & 0.26     & 0.25        & 0.19     & 0.25      &  & 0.33       \\
FineMedLM   ~\cite{finemedlm}                   &  & 0.40     & 0.35        & 0.27     & 0.38      &  & 0.30     & 0.34        & 0.38     & 0.31      &  & 0.34       \\
FineMedLM-o1   ~\cite{finemedlm}                &  & 0.43     & 0.34        & 0.26     & 0.39      &  & 0.34     & 0.38        & 0.40     & 0.35      &  & 0.37       \\
Bio-Medical-Llama-3-8B ~\cite{labrak2024biomistral}        &  & 0.53     & 0.41        & 0.38     & 0.49      &  & 0.48     & 0.47        & 0.49     & 0.48      &  & 0.48       \\
Internlm3-8b-Instruct ~\cite{internlm2}         &  & 0.60     & 0.43        & 0.40     & 0.54      &  & 0.85     & 0.76        & 0.77     & 0.84      &  & 0.69       \\
glm-4-9b-chat  ~\cite{chatglm}                &  & 0.48     & 0.36        & 0.36     & 0.44      &  & 0.61     & 0.60        & 0.56     & 0.61      &  & 0.53       \\
gemma-2-9b-it    ~\cite{Gemma}              &  & 0.53     & 0.40        & 0.36     & 0.49      &  & 0.54     & 0.49        & 0.41     & 0.52      &  & 0.51       \\
gemma-3-12b-it    ~\cite{gemma3}             &  & 0.56     & 0.46        & 0.36     & 0.52      &  & 0.59     & 0.55        & 0.51     & 0.58      &  & 0.55       \\
Baichuan2-13B-Chat  ~\cite{baichuan2023baichuan2}           &  & 0.42     & 0.31        & 0.34     & 0.39      &  & 0.48     & 0.47        & 0.46     & 0.48      &  & 0.43       \\
phi-4     ~\cite{phi}                     &  & 0.69     & 0.57        & 0.41     & 0.64      &  & 0.57     & 0.57        & 0.56     & 0.57      &  & 0.60       \\
DeepSeek-R1-Distill-Qwen-14B ~\cite{Deepseek-R1}  &  & 0.64     & 0.51        & 0.40     & 0.59      &  & 0.62     & 0.66        & 0.61     & 0.63      &  & 0.61       \\
Qwen2.5-14B-Instruct ~\cite{qwen2.5}          &  & 0.61     & 0.52        & 0.41     & 0.57      &  & 0.74     & 0.70        & 0.62     & 0.73      &  & 0.65       \\
Qwen3-14B ~\cite{qwen3} && 0.70&0.57&0.45&0.65&&0.77&0.72&0.68&0.76&&0.70 \\
gemma-3-27b-it   ~\cite{gemma3}              &  & 0.63     & 0.52        & 0.40     & 0.58      &  & 0.65     & 0.62        & 0.62     & 0.65      &  & 0.61       \\
gemma-2-27b-it    ~\cite{Gemma}             &  & 0.60     & 0.43        & 0.36     & 0.54      &  & 0.57     & 0.52        & 0.48     & 0.56      &  & 0.55       \\
Qwen3-30B-A3B    ~\cite{qwen3}              &  & 0.73     & 0.60        & 0.48     & 0.68      &  & 0.73     & 0.71        & 0.70     & 0.73      &  & 0.70       \\
Qwen3-32B   ~\cite{qwen3}                   &  & 0.72     & 0.64        & 0.48     & 0.68      &  & 0.80     & 0.77        & 0.73     & 0.80      &  & 0.74       \\
DeepSeek-R1-Distill-Qwen-32B ~\cite{Deepseek-R1}  &  & 0.67     & 0.56        & 0.45     & 0.63      &  & 0.66     & 0.71        & 0.65     & 0.67      &  & 0.65       \\
Qwen2.5-32B-Instruct   ~\cite{qwen2.5}        &  & 0.65     & 0.55        & 0.44     & 0.61      &  & 0.77     & 0.73        & 0.69     & 0.76      &  & 0.68       \\
QwQ-32B-Preview    ~\cite{qwq}            &  & 0.69     & 0.58        & 0.44     & 0.64      &  & 0.74     & 0.70        & 0.68     & 0.73      &  & 0.68       \\
Yi-1.5-34B-Chat  ~\cite{yi}              &  & 0.54     & 0.44        & 0.35     & 0.50      &  & 0.65     & 0.64        & 0.64     & 0.65      &  & 0.57       \\
Llama-3.3-70B-Instruct  ~\cite{Llama3}       &  & 0.74     & 0.63        & 0.51     & 0.70      &  & 0.69     & 0.66        & 0.63     & 0.68      &  & 0.69       \\
Llama-3-70B-UltraMedical   ~\cite{ultramedical}    &  & 0.73     & 0.60        & 0.47     & 0.68      &  & 0.72     & 0.68        & 0.62     & 0.71      &  & 0.69       \\
Llama3-OpenBioLLM-70B  ~\cite{OpenBioLLMs}        &  & 0.68     & 0.55        & 0.44     & 0.63      &  & 0.65     & 0.60        & 0.60     & 0.64      &  & 0.64       \\
Citrus1.0-llama-70B  ~\cite{Citrus}          &  & 0.71     & 0.60        & 0.52     & 0.67      &  & 0.71     & 0.69        & 0.67     & 0.71      &  & 0.69       \\
HuatuoGPT-o1-70B   ~\cite{huatuogpt}            &  & 0.70     & 0.58        & 0.48     & 0.65      &  & 0.70     & 0.70        & 0.64     & 0.70      &  & 0.68       \\
DeepSeek-R1-Distill-Llama-70B ~\cite{Deepseek-R1} &  & 0.77     & 0.68        & 0.56     & 0.73      &  & 0.64     & 0.64        & 0.59     & 0.64      &  & 0.68       \\
HuatuoGPT-o1-72B    ~\cite{huatuogpt}           &  & 0.71     & 0.61        & 0.48     & 0.67      &  & 0.82     & 0.78        & 0.78     & 0.81      &  & 0.74       \\
Qwen2.5-72B-Instruct ~\cite{qwen2.5}          &  & 0.72     & 0.60        & 0.48     & 0.67      &  & 0.82     & 0.77        & 0.76     & 0.81      &  & 0.74       \\
Qwen3-235B-A22B  ~\cite{qwen3}              &  & 0.78     & 0.67        & 0.57     & 0.74      &  & 0.76     & 0.73        & 0.69     & 0.75      &  & 0.74       \\
Llama-4-Scout-17B-16E-Instruct ~\cite{llama4} &  & 0.77     & 0.66        & 0.55     & 0.72      &  & 0.80     & 0.73        & 0.68     & 0.78      &  & 0.75       \\
deepseek-v3    ~\cite{Deepseek-v3}                &  & 0.77     & 0.69        & 0.55     & 0.73      &  & 0.79     & 0.77        & 0.70     & 0.78      &  & 0.76       \\
deepseek-r1    ~\cite{Deepseek-R1}                &  & 0.85     & 0.78        & 0.70     & 0.82      &  & 0.88     & 0.85        & 0.81     & 0.87      &  & 0.85       \\
gpt-4o    ~\cite{gpt4o-0513}                     &  & 0.81     & 0.72        & 0.59     & 0.77      &  & 0.78     & 0.77        & 0.68     & 0.78      &  & 0.77      \\
%  Qwen3-0.6B ~\cite{qwen3}                  &  & 0.39         & 0.31          & 0.26         & 0.36      &  & 0.37        & 0.35         & 0.31        & 0.37     &  & 0.36          \\
%   Gemma-3-1b-it ~\cite{gemma3}             &  & 0.33         & 0.26          & 0.21         & 0.30      &  & 0.26        & 0.25         & 0.21        & 0.26     &  & 0.28          \\
% Qwen3-1.7B   ~\cite{qwen3}               &  & 0.48         & 0.37          & 0.30         & 0.44      &  & 0.54        & 0.50         & 0.44        & 0.53     &  & 0.48          \\
% Qwen3-4B      ~\cite{qwen3}             &  & 0.60         & 0.46          & 0.34         & 0.54      &  & 0.54        & 0.48         & 0.48        & 0.53     &  & 0.53       \\
% Gemma-3-4b-it   ~\cite{gemma3}          &  & 0.46         & 0.36          & 0.35         & 0.42      &  & 0.43        & 0.41         & 0.37        & 0.43     &  & 0.42          \\
% ChatGLM3-6B~\cite{chatglm}                 &  & 0.37   & 0.28   & 0.25   & 0.34         &  & 0.36  & 0.36  & 0.34  & 0.36        &  & 0.35    \\
% Qwen2.5-7B-Instruct~\cite{qwen2.5}          &  & 0.56   & 0.44   & 0.36   & 0.52         &  & 0.68  & 0.63  & 0.58  & 0.67        &  & 0.60    \\
% HuatuoGPT-o1-7B~\cite{huatuogpt}               &  & 0.56   & 0.45   & 0.38   & 0.52         &  & 0.71  & 0.65  & 0.63  & 0.70        &  & 0.61    \\
% Baichuan2-7B-Chat~\cite{baichuan2023baichuan2}             &  & 0.39   & 0.31   & 0.30   & 0.37         &  & 0.44  & 0.41  & 0.41  & 0.43        &  & 0.39    \\
% BioMistral-7B~\cite{labrak2024biomistral}                 &  & 0.43   & 0.30   & 0.32   & 0.39         &  & 0.26  & 0.25  & 0.28  & 0.26        &  & 0.33    \\
% Meta-Llama-3-8B-Instruct~\cite{Llama3}      &  & 0.54   & 0.42   & 0.39   & 0.50         &  & 0.49  & 0.47  & 0.47  & 0.49        &  & 0.50    \\
% Llama-3.1-8B-Instruct~\cite{Llama3}         &  & 0.58   & 0.45   & 0.36   & 0.53         &  & 0.53  & 0.53  & 0.55  & 0.53        &  & 0.53    \\
% Llama-3.1-8B-UltraMedical~\cite{ultramedical}     &  & 0.63   & 0.47   & 0.41   & 0.57         &  & 0.54  & 0.52  & 0.50  & 0.54        &  & 0.56    \\
% HuatuoGPT-o1-8B~\cite{huatuogpt}               &  & 0.58   & 0.46   & 0.39   & 0.53         &  & 0.57  & 0.53  & 0.57  & 0.56        &  & 0.55    \\
% Llama3-OpenBioLLM-8B~\cite{OpenBioLLMs}          &  & 0.44   & 0.35   & 0.30   & 0.41         &  & 0.26  & 0.25  & 0.19  & 0.25        &  & 0.33    \\
% FineMedLM~\cite{finemedlm}                     &  & 0.40   & 0.35   & 0.27   & 0.38         &  & 0.30  & 0.34  & 0.38  & 0.31        &  & 0.35    \\
% FineMedLM-o1~\cite{finemedlm}                  &  & 0.43   & 0.34   & 0.26   & 0.39         &  & 0.34  & 0.38  & 0.40  & 0.35        &  & 0.37    \\
% Bio-Medical-Llama-3-8B~\cite{Bio-Medical-Llama-3-8B}        &  & 0.53   & 0.41   & 0.38   & 0.49         &  & 0.48  & 0.47  & 0.49  & 0.48        &  & 0.49    \\
% Internlm3-8b-instruct~\cite{internlm2}         &  & 0.60   & 0.43   & 0.40   & 0.54         &  & 0.85  & 0.76  & 0.77  & 0.84        &  & 0.69    \\
% ChatGLM-4-9b-chat~\cite{chatglm}                 &  & 0.48   & 0.36   & 0.36   & 0.44         &  & 0.61  & 0.60  & 0.56  & 0.61        &  & 0.53    \\
% Gemma-2-9b-it~\cite{Gemma}                 &  & 0.53   & 0.40   & 0.36   & 0.49         &  & 0.54  & 0.49  & 0.41  & 0.52        &  & 0.51    \\
% Gemma-3-12b-it   ~\cite{gemma3}              &  & 0.56         & 0.46          & 0.36         & 0.52      &  & 0.59        & 0.55         & 0.51        & 0.58     &  & 0.55          \\

% Baichuan2-13B-Chat~\cite{baichuan2023baichuan2}            &  & 0.42   & 0.31   & 0.34   & 0.39         &  & 0.48  & 0.47  & 0.46  & 0.48        &  & 0.44    \\
% Phi-4~\cite{phi}                         &  & 0.69   & 0.57   & 0.41   & 0.64         &  & 0.57  & 0.57  & 0.56  & 0.57        &  & 0.61    \\
% Qwen2.5-14B-Instruct~\cite{qwen2.5}          &  & 0.61   & 0.52   & 0.41   & 0.57         &  & 0.74  & 0.70  & 0.62  & 0.73        &  & 0.65    \\
% Gemma-2-27b-it~\cite{Gemma}                &  & 0.60   & 0.43   & 0.36   & 0.54         &  & 0.57  & 0.52  & 0.48  & 0.56        &  & 0.55    \\
% Gemma-3-27b-it ~\cite{gemma3}              &  & 0.63         & 0.52          & 0.40         & 0.58      &  & 0.65        & 0.62         & 0.62        & 0.65     &  & 0.61          \\
% Qwen2.5-32B-Instruct~\cite{qwen2.5}          &  & 0.65   & 0.55   & 0.44   & 0.61         &  & 0.77  & 0.73  & 0.69  & 0.76        &  & 0.69    \\
% QwQ-32B-Preview~\cite{qwq}               &  & 0.69   & 0.58   & 0.44   & 0.64         &  & 0.74  & 0.70  & 0.68  & 0.73        &  & 0.69    \\
% Yi-1.5-34B-Chat~\cite{yi}               &  & 0.54   & 0.44   & 0.35   & 0.50         &  & 0.65  & 0.64  & 0.64  & 0.65        &  & 0.58    \\
% HuatuoGPT-o1-70B~\cite{huatuogpt}              &  & 0.70   & 0.58   & 0.48   & 0.65         &  & 0.70  & 0.70  & 0.64  & 0.70        &  & 0.68    \\
% Llama-3-70B-UltraMedical~\cite{ultramedical}      &  & 0.73   & 0.60   & 0.47   & 0.68         &  & 0.72  & 0.68  & 0.62  & 0.71        &  & 0.70    \\
% Llama3-OpenBioLLM-70B~\cite{OpenBioLLMs}         &  & 0.68   & 0.55   & 0.44   & 0.63         &  & 0.65  & 0.60  & 0.60  & 0.64        &  & 0.64    \\
% Llama-3.3-70B-Instruct~\cite{Llama3}        &  & 0.74   & 0.63   & 0.51   & 0.70         &  & 0.69  & 0.66  & 0.63  & 0.68        &  & 0.69    \\
% Citrus1.0-llama-70B~\cite{Citrus}           &  & 0.71   & 0.60   & 0.52   & 0.67         &  & 0.71  & 0.69  & 0.67  & 0.71        &  & 0.69 \\
% Qwen2.5-72B-Instruct~\cite{qwen2.5}          &  & 0.72   & 0.60   & 0.48   & 0.67         &  & 0.82  & 0.77  & 0.76  & 0.81        &  & 0.74    \\
% HuatuoGPT-o1-72B~\cite{huatuogpt}              &  & 0.71   & 0.61   & 0.48   & 0.67         &  & 0.82  & 0.78  & 0.78  & 0.81        &  & 0.74    \\
% Llama-4-Scout-17B-16E-Instruct ~\cite{llama4} &  & 0.77         & 0.66          & 0.55         & 0.72      &  & 0.80        & 0.73         & 0.68        & 0.78     &  & 0.75          \\
% R1-Distill-Qwen-1.5B~\cite{Deepseek-R1} &  & 0.32   & 0.28   & 0.27   & 0.31         &  & 0.26  & 0.26  & 0.22  & 0.26        &  & 0.29    \\
% R1-Distill-Qwen-7B~\cite{Deepseek-R1}   &  & 0.40   & 0.34   & 0.28   & 0.38         &  & 0.33  & 0.32  & 0.34  & 0.33        &  & 0.36    \\
% R1-Distill-Llama-8B~\cite{Deepseek-R1}  &  & 0.52   & 0.39   & 0.34   & 0.47         &  & 0.33  & 0.31  & 0.36  & 0.33        &  & 0.40    \\
% R1-Distill-Qwen-14B~\cite{Deepseek-R1}  &  & 0.64   & 0.51   & 0.40   & 0.59         &  & 0.62  & 0.66  & 0.61  & 0.63        &  & 0.61    \\
% R1-Distill-Qwen-32B~\cite{Deepseek-R1}~\cite{Deepseek-R1}  &  & 0.67   & 0.56   & 0.45   & 0.63         &  & 0.66  & 0.71  & 0.65  & 0.67        &  & 0.65    \\
% R1-Distill-Llama-70B~\cite{Deepseek-R1} &  & 0.77   & 0.68   & 0.56   & 0.73         &  & 0.64  & 0.64  & 0.59  & 0.64        &  & 0.69    \\
% DeepSeek-V3~\cite{Deepseek-v3}                   &  & 0.77   & 0.69   & 0.55   & 0.73         &  & 0.79  & 0.77  & 0.70  & 0.78        &  & 0.76    \\
% DeepSeek-R1~\cite{Deepseek-R1}                  &  & 0.85   & 0.78   & 0.70   & 0.82         &  & 0.88  & 0.85  & 0.81  & 0.87        &  & 0.85    \\
% GPT-4o~\cite{gpt4o-0513}                   &  & 0.81   & 0.72   & 0.59   & 0.77         &  & 0.78  & 0.77  & 0.68  & 0.78        &  & 0.78    \\
\bottomrule
\end{tabular}
}
\label{tab:moreResult_0}
\end{table*}

        
% \begin{table*}[h]
% \caption{\textbf{Complete Evaluation Results on \anesbench and AMCQA}.}
% \setlength\extrarowheight{3.2pt}
% \renewcommand{\arraystretch}{0.85}
% \setlength{\tabcolsep}{2pt}
% \resizebox{\textwidth}{!}{
% \begin{tabular}{lp{1.5mm}rrrrp{1.5mm}rrrrp{1.5mm}c}
% \toprule
% \multirow{2}{*}{Model} && \multicolumn{4}{c}{\textbf{\anesbench}} && \multicolumn{4}{c}{\textbf{AMCQA}} && \multirow{2}{*}{\textbf{Overall}} \\
% \cline{3-6} \cline{8-11}
%  && Sys1 & Sys1.x & Sys2 & Total && Sys1 & Sys1.x & Sys2 & Total && \\
%  \midrule
%  \multicolumn{13}{c}{\textbf{\textasciitilde7B models}} \\
%  \midrule
% ChatGLM3-6B~\cite{chatglm}                 &  & 0.37   & 0.28   & 0.25   & 0.34         &  & 0.36  & 0.36  & 0.34  & 0.36        &  & 0.35    \\
% Qwen2.5-7B-Instruct~\cite{qwen2.5}          &  & 0.56   & 0.44   & 0.36   & 0.52         &  & 0.68  & 0.63  & 0.58  & 0.67        &  & 0.61    \\
% HuatuoGPT-o1-7B~\cite{huatuogpt}               &  & 0.56   & 0.45   & 0.38   & 0.52         &  & 0.71  & 0.65  & 0.63  & 0.70        &  & 0.63    \\
% Baichuan2-7B-Chat~\cite{baichuan2023baichuan2}             &  & 0.39   & 0.31   & 0.30   & 0.37         &  & 0.44  & 0.41  & 0.41  & 0.43        &  & 0.41    \\
% BioMistral-7B~\cite{labrak2024biomistral}                 &  & 0.43   & 0.30   & 0.32   & 0.39         &  & 0.26  & 0.25  & 0.28  & 0.26        &  & 0.31    \\
% Meta-Llama-3-8B-Instruct~\cite{Llama3}      &  & 0.54   & 0.42   & 0.39   & 0.50         &  & 0.49  & 0.47  & 0.47  & 0.49        &  & 0.49    \\
% Llama-3.1-8B-Instruct~\cite{Llama3}         &  & 0.58   & 0.45   & 0.36   & 0.53         &  & 0.53  & 0.53  & 0.55  & 0.53        &  & 0.53    \\
% Llama-3.1-8B-UltraMedical~\cite{ultramedical}     &  & 0.63   & 0.47   & 0.41   & 0.57         &  & 0.54  & 0.52  & 0.50  & 0.54        &  & 0.55    \\
% HuatuoGPT-o1-8B~\cite{huatuogpt}               &  & 0.58   & 0.46   & 0.39   & 0.53         &  & 0.57  & 0.53  & 0.57  & 0.56        &  & 0.55    \\
% Llama3-OpenBioLLM-8B~\cite{OpenBioLLMs}          &  & 0.44   & 0.35   & 0.30   & 0.41         &  & 0.26  & 0.25  & 0.19  & 0.25        &  & 0.31    \\
% FineMedLM~\cite{finemedlm}                     &  & 0.40   & 0.35   & 0.27   & 0.38         &  & 0.30  & 0.34  & 0.38  & 0.31        &  & 0.34    \\
% FineMedLM-o1~\cite{finemedlm}                  &  & 0.43   & 0.34   & 0.26   & 0.39         &  & 0.34  & 0.38  & 0.40  & 0.35        &  & 0.37    \\
% Bio-Medical-Llama-3-8B~\cite{Bio-Medical-Llama-3-8B}        &  & 0.53   & 0.41   & 0.38   & 0.49         &  & 0.48  & 0.47  & 0.49  & 0.48        &  & 0.48    \\
% Internlm3-8b-instruct~\cite{internlm2}         &  & 0.60   & 0.43   & 0.40   & 0.54         &  & 0.85  & 0.76  & 0.77  & 0.84        &  & 0.73    \\
% ChatGLM-4-9b-chat~\cite{chatglm}                 &  & 0.48   & 0.36   & 0.36   & 0.44         &  & 0.61  & 0.60  & 0.56  & 0.61        &  & 0.55    \\
% Gemma-2-9b-it~\cite{Gemma}                 &  & 0.53   & 0.40   & 0.36   & 0.49         &  & 0.54  & 0.49  & 0.41  & 0.52        &  & 0.51    \\
%  \midrule
%  \multicolumn{13}{c}{\textbf{> 10B models}} \\
%  \midrule
% Baichuan2-13B-Chat~\cite{baichuan2023baichuan2}            &  & 0.42   & 0.31   & 0.34   & 0.39         &  & 0.48  & 0.47  & 0.46  & 0.48        &  & 0.45    \\
% Phi-4~\cite{phi}                         &  & 0.69   & 0.57   & 0.41   & 0.64         &  & 0.57  & 0.57  & 0.56  & 0.57        &  & 0.59    \\
% Qwen2.5-14B-Instruct~\cite{qwen2.5}          &  & 0.61   & 0.52   & 0.41   & 0.57         &  & 0.74  & 0.70  & 0.62  & 0.73        &  & 0.67    \\
% Gemma-2-27b-it~\cite{Gemma}                &  & 0.60   & 0.43   & 0.36   & 0.54         &  & 0.57  & 0.52  & 0.48  & 0.56        &  & 0.55    \\
% Qwen2.5-32B-Instruct~\cite{qwen2.5}          &  & 0.65   & 0.55   & 0.44   & 0.61         &  & 0.77  & 0.73  & 0.69  & 0.76        &  & 0.70    \\
% QwQ-32B-Preview~\cite{qwq}               &  & 0.69   & 0.58   & 0.44   & 0.64         &  & 0.74  & 0.70  & 0.68  & 0.73        &  & 0.70    \\
% Yi-1.5-34B-Chat~\cite{yi}               &  & 0.54   & 0.44   & 0.35   & 0.50         &  & 0.65  & 0.64  & 0.64  & 0.65        &  & 0.59    \\
% HuatuoGPT-o1-70B~\cite{huatuogpt}              &  & 0.70   & 0.58   & 0.48   & 0.65         &  & 0.70  & 0.70  & 0.64  & 0.70        &  & 0.68    \\
% Llama-3-70B-UltraMedical~\cite{ultramedical}      &  & 0.73   & 0.60   & 0.47   & 0.68         &  & 0.72  & 0.68  & 0.62  & 0.71        &  & 0.70    \\
% Llama3-OpenBioLLM-70B~\cite{OpenBioLLMs}         &  & 0.68   & 0.55   & 0.44   & 0.63         &  & 0.65  & 0.60  & 0.60  & 0.64        &  & 0.64    \\
% Llama-3.3-70B-Instruct~\cite{Llama3}        &  & 0.74   & 0.63   & 0.51   & 0.70         &  & 0.69  & 0.66  & 0.63  & 0.68        &  & 0.69    \\
% Citrus1.0-llama-70B~\cite{Citrus}           &  & 0.71   & 0.60   & 0.52   & 0.67         &  & 0.71  & 0.69  & 0.67  & 0.71        &  & 0.69 \\
% Qwen2.5-72B-Instruct~\cite{qwen2.5}          &  & 0.72   & 0.60   & 0.48   & 0.67         &  & 0.82  & 0.77  & 0.76  & 0.81        &  & 0.76    \\
% HuatuoGPT-o1-72B~\cite{huatuogpt}              &  & 0.71   & 0.61   & 0.48   & 0.67         &  & 0.82  & 0.78  & 0.78  & 0.81        &  & 0.76    \\
%  \midrule
%  \multicolumn{13}{c}{\textbf{DeepSeek family \& GPT}} \\
%  \midrule
% R1-Distill-Qwen-1.5B~\cite{Deepseek-R1} &  & 0.32   & 0.28   & 0.27   & 0.31         &  & 0.26  & 0.26  & 0.22  & 0.26        &  & 0.28    \\
% R1-Distill-Qwen-7B~\cite{Deepseek-R1}   &  & 0.40   & 0.34   & 0.28   & 0.38         &  & 0.33  & 0.32  & 0.34  & 0.33        &  & 0.35    \\
% R1-Distill-Llama-8B~\cite{Deepseek-R1}  &  & 0.52   & 0.39   & 0.34   & 0.47         &  & 0.33  & 0.31  & 0.36  & 0.33        &  & 0.38    \\
% R1-Distill-Qwen-14B~\cite{Deepseek-R1}  &  & 0.64   & 0.51   & 0.40   & 0.59         &  & 0.62  & 0.66  & 0.61  & 0.63        &  & 0.61    \\
% R1-Distill-Qwen-32B~\cite{Deepseek-R1}~\cite{Deepseek-R1}  &  & 0.67   & 0.56   & 0.45   & 0.63         &  & 0.66  & 0.71  & 0.65  & 0.67        &  & 0.65    \\
% R1-Distill-Llama-70B~\cite{Deepseek-R1} &  & 0.77   & 0.68   & 0.56   & 0.73         &  & 0.64  & 0.64  & 0.59  & 0.64        &  & 0.67    \\
% DeepSeek-V3~\cite{Deepseek-v3}                   &  & 0.77   & 0.69   & 0.55   & 0.73         &  & 0.79  & 0.77  & 0.70  & 0.78        &  & 0.77    \\
% DeepSeek-R1~\cite{Deepseek-R1}                  &  & 0.85   & 0.78   & 0.70   & 0.82         &  & 0.88  & 0.85  & 0.81  & 0.87        &  & 0.85    \\
% GPT-4o~\cite{gpt4o-0513}                   &  & 0.81   & 0.72   & 0.59   & 0.77         &  & 0.78  & 0.77  & 0.68  & 0.78        &  & 0.77    \\
% \bottomrule
% \end{tabular}
% }
% \label{tab:moreResult_0}
% \end{table*}



\clearpage
\section{Prompt for Question Categorization}

\label{appendix:questionCategorization}

We utilize Deepseek-R1 for multi-dimensional, few-shot question categorization. For models lacking system prompt support, we integrate the system prompt into the user prompt. The categorization process follows precise analytical guidelines and illustrative examples.

% \begin{figure}[h]
%     \centering
\begin{tcolorbox}[colback=gray!10!white,colframe=black!30!white,left=2mm, right=2mm,title=\centering\small\textcolor{black}{{\fontsize{12pt}{10pt}\selectfont \textbf{Prompt for Question Categorization}}}]

{\fontsize{11pt}{10pt}\selectfont \textbf{System Prompt:}} \\

You are an anesthesiologist and helpful personal assistant, proficient in categorizing questions strictly according to user instructions. \\[2ex]

{\fontsize{11pt}{10pt}\selectfont \textbf{User Prompt:}} \\


\textbf{Problem description:}\\[1ex]
\texttt{[\{instance["problem"]\}]}\\[1ex]
\texttt{A: [\{instance["A"]\}]}\\[1ex]
\texttt{B: [\{instance["B"]\}]}\\[1ex]
\texttt{......}\\[1ex]

\textbf{Answer:}\\[1ex]
\texttt{[\{instance["answer"]\}]}\\[1ex]

\textbf{Classification Categories:}\\[1ex]
1. \textbf{Knowledge-Based}: Involves recalling fundamental facts, definitions, or procedures. This type of question primarily tests factual memory without requiring application or in-depth reasoning. For example: ``What is the standard dose of Propofol for an adult patient undergoing general anesthesia?''\\[1ex]

2. \textbf{Application-Based}: Involves using basic knowledge or concepts in straightforward situations. The question requires applying known information to solve problems or make decisions, but does not require complex thinking or multiple-step reasoning. For example: ``If a patient has a history of allergies to local anesthetics, which alternative drug should be considered?''\\[1ex]

3. \textbf{Reasoning-Based}: Requires logical thinking, analysis, and the use of problem-solving skills. This type of question challenges the ability to evaluate complex situations, reason through different factors, and make informed decisions. For example: ``Given the patient's medical history and lab results, determine the most appropriate anesthetic technique for the upcoming surgery.''\\[2ex]

\textbf{Instructions for Classification:}\\[1ex]
Please categorize the problem by selecting the most appropriate category that aligns with the type of thinking and approach required to address the question. Consider factors such as the level of complexity, the type of cognitive processing needed, and whether the problem involves straightforward recall, application, or in-depth reasoning. Your response should conclude with: \textbf{``So, the problem can be categorized as $\boxed{\{ANSWER\}}$,''} where \textbf{ANSWER} is one of the numbers 1, 2, or 3.
\end{tcolorbox}
%     \caption{The prompt used for question categorization.}
%     \label{text:problem_cate}
% \end{figure}



\clearpage
\section{Prompt for Question Translation}
\label{appendix:questionTranslation}

We use ChatGPT-4o to translate all questions. Additionally, we provide detailed translation instructions and formatting requirements in the prompt.


% \begin{figure}[htbp]
%     \centering
    \begin{tcolorbox}[colback=gray!10!white,colframe=black!30!white,,left=2mm,right=2mm,title=\centering\small\textcolor{black}{{\fontsize{12pt}{10pt}\selectfont \textbf{Prompt for Question Translation}}}]
    
{\fontsize{11pt}{10pt}\selectfont \textbf{System Prompt:}} \\[1ex]
You are a translator and helpful assistant proficient in the field of anesthesiology, strictly completing translation tasks according to the user's requirements. \\[2ex]

{\fontsize{11pt}{10pt}\selectfont \textbf{User Prompt:}} \\[1ex]

Translate the following English multiple-choice question into Chinese and output it in the specfic format: \\[2ex]

{\fontsize{11pt}{10pt}\selectfont English question:} \\[1ex]
\{ \\[1ex]
\texttt{"question":[\{instance["problem"]\}]}\\[1ex]
\texttt{A: [\{instance["A"]\}]}\\[1ex]
\texttt{B: [\{instance["B"]\}]}\\[1ex]
\texttt{......}\\[2ex]
\} \\[1ex]


{\fontsize{11pt}{10pt}\selectfont Format:} \\[1ex]
\{ \\[1ex]
\texttt{"question": "\cn{中文问题}",}\\[1ex]
\texttt{A: \cn{选项A}}\\[1ex]
\texttt{B: \cn{选项B}}\\[1ex]
\texttt{......}\\[2ex]
\} \\[1ex]

Do not include any additional reasoning or explanations, only provide the translated content.
    \end{tcolorbox}
%     \caption{The prompt used for question translation.}
%     \label{text:translation_prompt}
% \end{figure}

\clearpage

\section{Overview of CPT and SFT datasets}
\label{appendix:datasets}

\begin{table}[ht]
    \centering
    \setlength{\tabcolsep}{7pt}
    \renewcommand{\arraystretch}{1.2}
    \begin{tabular}{@{}llcc@{}}
        \toprule
        \textbf{Dataset} & & \textbf{AnesCorpus (CPT)} & \textbf{AnesQA (SFT)} \\ 
        \midrule
        \multirow{2}{*}{Source} & & PubMed articles, & GPT-generated \\
                                     & & FineWeb corpus & QA pairs \\
        \midrule
        Data Type & &  Text corpus & Question-Answer pairs \\
        \midrule
        Language &  & English/Chinese & English/Chinese \\
        \midrule
        \multirow{4}{*}{Data Scale} & \multirow{2}{*}{EN} 
        & $\sim$ 18.5M docs  & $\sim$ 20k QA-pairs \\
        & & ($\sim$ 3B tokens)  & ($\sim$ 4M tokens) \\ 
        \cline{2-4}
        &  \multirow{2}{*}{CH} 
        & $\sim$ 6M docs  & $\sim$ 20k QA-pairs \\
        & & ($\sim$ 0.2B tokens)  &  ($\sim$ 6M tokens) \\
        \bottomrule
    \end{tabular}
    \vspace{10pt}
    \caption{CPT and SFT Dataset Specifications}
    \label{tab:cpt_sft}
\end{table}


\section{Word Cloud Analysis of Corpus}
\label{sec:corpus}
As illustrated in Fig.~\ref{fig:word_cloud}, our constructed anesthesia-related corpus encompasses a diverse range of medical terminologies, both in English and Chinese. The English word cloud highlights key terms such as ``general anesthesia'', ``local anesthetic'', ``surgical procedure'', and ``pain management'', reflecting a strong focus on clinical applications, patient care, and perioperative considerations. Additionally, frequent mentions of terms like ``nerve block'', ``blood pressure'', and ``side effect'' indicate a broad coverage of anesthesia techniques and associated physiological impacts.
Similarly, the Chinese word cloud presents a comparable distribution, with dominant terms such as ``\cn{手术}(surgery)'', ``\cn{治疗}(treatment)'', ``\cn{麻醉}(anesthesia)'', indicating a strong alignment with procedural and treatment-related aspects. The presence of terms related to medical interventions, such as ``\cn{气管插管}(intubation)'' and ``\cn{药物治疗}(medication)'', exhibits emphasis on clinical procedures and pharmacological considerations.

\begin{figure}[h]
\centering
   \begin{subfigure}{0.48\linewidth}
\centering
    \includegraphics[width=0.7\linewidth]{figs/wordcloud.pdf}
\end{subfigure}
\hspace{-0.4in}
\begin{subfigure}{0.48\linewidth}
\centering
    \includegraphics[width=0.7\linewidth]{figs/wordcloud_cn.pdf}
\end{subfigure}
    \caption{Word clouds of CPT corpus.}
    \label{fig:word_cloud}
\end{figure}


\section{Data Leakage Detection}

\label{sec:dataLeakage}

Given the increasing volume of pre-training data utilized by large language models, coupled with the fact that the sources and specific acquisition methods are often regarded as core secrets and rarely disclosed, a pertinent issue arises: whether the questions in our benchmark have been inadvertently leaked into the pre-training data of various models. We have employed a method specifically designed for multiple-choice questions to detect potential data leakage~\cite{DataLeakageDetect}. For a given multiple-choice instance \( x = (x_{\text{stem}}, o_1, o_2, \dots, o_n) \), where \( x_{\text{stem}} \) represents the question stem and \( o_1, o_2, \dots, o_n \) are the options, we generate all possible permutations of the options, resulting in \( \{x_1, x_2, \dots, x_{n!}\} \). For each permutation, the sequence is decomposed into tokens as \( x = (t_1, t_2, \dots, t_m) \). We then compute the confidence score for each sequence using the following method:


\begin{align}
    \text{Conf}_{\text{LLM}}(x) = \left( \prod_{i=\text{1}}^{m-1} p_{\text{LLM}}(t_i | x) \right)^{\frac{1}{m-1}}.
    \label{eq:conf}
\end{align}



If the original order \( x_0 \) has the highest confidence score among all permutations, we consider that the multiple-choice instance \textbf{may} have experienced data leakage. We randomly select 500 questions from \anesbench, excluding those with excessive options. Under the assumption that the LLM has no prior knowledge and calculates probabilities randomly, this method yields a potential leakage proportion of 0.04. As shown in Fig. \ref{fig:DataLeakage}, most models exhibit a potential data leakage proportion around 0.10, with the exception of the Qwen and Yi model families, which are slightly higher (consistent with the evaluation results of MMLU\cite{MMLU}, CMMLU\cite{CMMLU}, C-Eval\cite{C-Eval}, and CMB\cite{CMB} in the original text~\cite{DataLeakageDetect}). Overall, the low potential leakage proportions of most models on \anesbench validate the quality of our work.


\label{dataLeakage}

\begin{figure}[h]
    \centering
    \includegraphics[width=1\textwidth]{figs/data_leakage_proportion.pdf}
    \caption{\textbf{Proportion of potential data leakage in the model}. Each $\blacktriangle$ below the x-axis denotes a model family.}
    \label{fig:DataLeakage}
\end{figure}

\documentclass{article}
\usepackage{multirow}
\usepackage{booktabs}
\usepackage{colortbl}
\usepackage{graphicx}
\usepackage{caption}
\usepackage{subcaption}
\usepackage{wrapfig}
\usepackage{tcolorbox}
\usepackage{amssymb}    % 提供 \blacktriangle 等符号

% 定义颜色
\definecolor{highest}{RGB}{255,111,97} % 最高分颜色（浅蓝色）
\definecolor{second}{RGB}{0,206,209} % 次高分颜色（浅绿色）


% if you need to pass options to natbib, use, e.g.:
%     \PassOptionsToPackage{numbers, compress}{natbib}
% before loading neurips_2024


% ready for submission
%\usepackage{neurips_2024}


% to compile a preprint version, e.g., for submission to arXiv, add add the
% [preprint] option:
\usepackage[preprint]{neurips_2024}


% to compile a camera-ready version, add the [final] option, e.g.:
%     \usepackage[final]{neurips_2024}


% to avoid loading the natbib package, add option nonatbib:
%    \usepackage[nonatbib]{neurips_2024}


\usepackage[utf8]{inputenc} % allow utf-8 input
\usepackage[T1]{fontenc}    % use 8-bit T1 fonts
\usepackage{hyperref}       % hyperlinks
\usepackage{url}            % simple URL typesetting
\usepackage{booktabs}       % professional-quality tables
\usepackage{amsfonts}       % blackboard math symbols
\usepackage{nicefrac}       % compact symbols for 1/2, etc.
\usepackage{microtype}      % microtypography
\usepackage{xcolor}         % colors
\usepackage{graphicx}
\usepackage{tcolorbox}
\usepackage{pifont}
\usepackage{tikz}
\usepackage{enumitem}
\setlist[itemize]{leftmargin=*}

\newcommand{\tikzxmark}{
\tikz[scale=0.23] {
    \draw[line width=0.7,line cap=round] (0,0) to [bend left=6] (1,1);
    \draw[line width=0.7,line cap=round] (0.2,0.95) to [bend right=3] (0.8,0.05);
}}
\newcommand{\tikzcmark}{
\tikz[scale=0.23] {
    \draw[line width=0.7,line cap=round] (0.25,0) to [bend left=10] (1,1);
    \draw[line width=0.8,line cap=round] (0,0.35) to [bend right=1] (0.23,0);
}}

\newcommand{\anesbench}{\textsc{AnesBench}\xspace}
\newcommand{\cmark}{\ding{51}}
\newcommand{\xmark}{\ding{55}}

\definecolor{jwtcolor}{RGB}{0, 123, 255}
\newcommand{\jwt}[1]{{\color{jwtcolor}#1}}

\input{Styles/math_command}

\begin{document}

\input{pages/#.Title}
\maketitle



\input{pages/0.Abstract}
\input{pages/1.Introduction}
\input{pages/2.Related Work}
\input{pages/3.Method}
\input{pages/4.Experiments and Results}
\input{pages/5.Conclusion}

{
\bibliographystyle{unsrt}
\bibliography{bibs/main}
}


\appendix
\input{pages/a.Appendix}
\input{pages/7.Datasheet}

\end{document}
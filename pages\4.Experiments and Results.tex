\section{Impact of Model Characteristics}

In this section, we conduct a comprehensive evaluation of state-of-the-art LLMs. Through rigorous analysis of the results, we derived several key insights. These insights contribute to a deeper understanding of the potential and limitations of LLMs in this domain.


% Only the one with the highest score in the average score column is presented here, for models with the same parameter size.
\begin{table*}[t]
\caption{\textbf{Main Evaluation Results on AnesBench and AMCQA:} The highest and second-highest scores in each columns are highlighted in bold and underlined, respectively. Only a selection of representative models is presented here.  Complete evaluation results are in Table~\ref{tab:moreResult_0} in Appendix~\ref{appendix:moreResult}.}
\setlength\extrarowheight{0pt}
\renewcommand{\arraystretch}{0.9}
\setlength{\tabcolsep}{1pt}
% \resizebox{\textwidth}{!}{
\centering
\begin{tabular}{lp{1.5mm}ccccp{1.5mm}ccccp{1.5mm}c}
\toprule
\multirow{2}{*}{Model} && \multicolumn{4}{c}{\textbf{\anesbench}} && \multicolumn{4}{c}{\textbf{AMCQA}} && \multirow{2}{*}{\textbf{Avg.}} \\
\cline{3-6} \cline{8-11}
 && Sys1 & Sys1.x & Sys2 & Total && Sys1 & Sys1.x & Sys2 & Total && \\
 \midrule
 % \multicolumn{13}{c}{\textbf{4\textasciitilde10B models}} \\
  \multicolumn{13}{c}{\textbf{Open-Source LLMs}} \\
 \midrule
%  Qwen3-4B      ~\cite{qwen3}             &  & 0.60         & 0.46          & 0.34         & 0.54      &  & 0.54        & 0.48         & 0.48        & 0.53     &  & 0.53       \\
% ChatGLM3-6B~\cite{chatglm}                 &  & 0.37   & 0.28   & 0.25   & 0.34         &  & 0.36  & 0.36  & 0.34  & 0.36        &  & 0.35    \\
% HuatuoGPT-o1-7B~\cite{huatuogpt}               &  & 0.56   & 0.45   & 0.38   & 0.52         &  & 0.71  & 0.65  & 0.63  & 0.70        &  & 0.61    \\
% Internlm3-8b-instruct~\cite{internlm2}         &  & 0.60   & 0.43   & 0.40   & 0.54         &  & 0.85  & 0.76  & 0.77  & 0.84        &  & 0.69    \\
% ChatGLM-4-9b-chat~\cite{chatglm}                 &  & 0.48   & 0.36   & 0.36   & 0.44         &  & 0.61  & 0.60  & 0.56  & 0.61        &  & 0.53    \\
HuatuoGPT-o1-7B   ~\cite{huatuogpt}             &  & 0.56     & 0.45        & 0.38     & 0.52      &  & 0.71     & 0.65        & 0.63     & 0.70      &  & 0.61       \\
% Internlm3-8b-Instruct ~\cite{internlm2}         &  & 0.60     & 0.43        & 0.40     & 0.54      &  & \underline{0.85}     & 0.76        & \underline{0.77 }    & 0.84      &  & 0.69       \\
Qwen2.5-14B-Instruct ~\cite{qwen2.5}          &  & 0.61     & 0.52        & 0.41     & 0.57      &  & 0.74     & 0.70        & 0.62     & 0.73      &  & 0.65       \\
Qwen3-32B   ~\cite{qwen3}                   &  & 0.72     & 0.64        & 0.48     & 0.68      &  & \underline{0.80}     & \underline{0.77}        & \underline{0.73}     & \underline{0.80}     &  & 0.74       \\
Llama-3.3-70B-Instruct  ~\cite{Llama3}       &  & 0.74     & 0.63        & 0.51     & 0.70      &  & 0.69     & 0.66        & 0.63     & 0.68      &  & 0.69       \\
Llama-3-70B-UltraMedical   ~\cite{ultramedical}    &  & 0.73     & 0.60        & 0.47     & 0.68      &  & 0.72     & 0.68        & 0.62     & 0.71      &  & 0.69       \\
Citrus1.0-llama-70B  ~\cite{Citrus}          &  & 0.71     & 0.60        & 0.52     & 0.67      &  & 0.71     & 0.69        & 0.67     & 0.71      &  & 0.69       \\
Qwen3-235B-A22B  ~\cite{qwen3}              &  & 0.78     & 0.67        & 0.57     & 0.74      &  & 0.76     & 0.73        & 0.69     & 0.75      &  & 0.74       \\
Llama-4-Scout-17B-16E-Instruct ~\cite{llama4} &  & 0.77     & 0.66        & 0.55     & 0.72      &  & \underline{0.80}     & 0.73        & 0.68     & 0.78      &  & 0.75       \\
 % \midrule
 % \multicolumn{13}{c}{\textbf{>10B models}} \\
 % \midrule
DeepSeek-V3~\cite{Deepseek-v3}                   &  & 0.77   & 0.69   & 0.55   & 0.73         &  & 0.79  & \underline{0.77}  & 0.70  & 0.78        &  & 0.76    \\
DeepSeek-R1~\cite{Deepseek-R1}                  &  & \textbf{0.85}   &\textbf{0.78}    &  \textbf{0.70}   & \textbf{0.82}        &  & \textbf{0.88}  & \textbf{0.85 } & \textbf{0.81 } & \textbf{0.87}  &  & \textbf{0.85}    \\
 \midrule
 \multicolumn{13}{c}{\textbf{Closed-Source LLM}} \\
 \midrule
 GPT-4o~\cite{gpt4o-0513}                   &  & \underline{0.81}   & \underline{0.72}   & \underline{0.59}  & \underline{0.77}         &  & 0.78  & \underline{0.77}  & 0.68  & 0.78        &  & \underline{0.77}    \\
% ChatGLM3-6B                   &  & 0.37   & 0.28   & 0.25   & 0.34         &  & 0.36  & 0.36  & 0.34  & 0.36        &  & 0.35    \\
% HuatuoGPT-o1-7B               &  & 0.56   & 0.45   & 0.38   & 0.52         &  & 0.71  & 0.65  & 0.63  & 0.70        &  & 0.63    \\
% Internlm3-8b-instruct         &  & 0.60   & 0.43   & 0.40   & 0.54         &  & 0.85  & 0.76  & 0.77  & 0.84        &  & 0.73    \\
% Glm-4-9b-chat                 &  & 0.48   & 0.36   & 0.36   & 0.44         &  & 0.61  & 0.60  & 0.56  & 0.61        &  & 0.55    \\
%  \midrule
%  \multicolumn{13}{c}{\textbf{> 10B models}} \\
%  \midrule
% Baichuan2-13B-Chat            &  & 0.42   & 0.31   & 0.34   & 0.39         &  & 0.48  & 0.47  & 0.46  & 0.48        &  & 0.45    \\
% Qwen2.5-14B-Instruct          &  & 0.61   & 0.52   & 0.41   & 0.57         &  & 0.74  & 0.70  & 0.62  & 0.73        &  & 0.67    \\
% Gemma-2-27b-it                &  & 0.60   & 0.43   & 0.36   & 0.54         &  & 0.57  & 0.52  & 0.48  & 0.56        &  & 0.55    \\
% Qwen2.5-32B-Instruct          &  & 0.65   & 0.55   & 0.44   & 0.61         &  & 0.77  & 0.73  & 0.69  & 0.76        &  & 0.70    \\
% QwQ-32B-Preview               &  & 0.69   & 0.58   & 0.44   & 0.64         &  & 0.74  & 0.70  & 0.68  & 0.73        &  & 0.70    \\
% Llama-3-70B-UltraMedical      &  & 0.73   & 0.60   & 0.47   & 0.68         &  & 0.72  & 0.68  & 0.62  & 0.71        &  & 0.70    \\
% Qwen2.5-72B-Instruct          &  & 0.72   & 0.60   & 0.48   & 0.67         &  & 0.82  & 0.77  & 0.76  & 0.81        &  & 0.76    \\
% HuatuoGPT-o1-72B              &  & 0.71   & 0.61   & 0.48   & 0.67         &  & 0.82  & 0.78  & 0.78  & 0.81        &  & 0.76    \\
%  \midrule
%  \multicolumn{13}{c}{\textbf{DeepSeek V3/R1 \& GPT-4o}} \\
%  \midrule
% DeepSeek-V3                   &  & 0.77   & 0.69   & 0.55   & 0.73         &  & 0.79  & 0.77  & 0.70  & 0.78        &  &  \underline{0.77}    \\
% DeepSeek-R1                   &  & 0.85   & 0.78   & 0.70   & 0.82         &  & 0.88  & 0.85  & 0.81  & 0.87        &  & \textbf{0.85}    \\
% GPT-4o                        &  & 0.81   & 0.72   & 0.59   & 0.77         &  & 0.78  & 0.77  & 0.68  & 0.78        &  & \underline{0.77}    \\
\bottomrule
\end{tabular}
% }
\label{tab:benchmark_results}
\end{table*}

\subsection{Evaluation Setup}

% \paragraph{Experimental Settings}
\paragraph{Decoding Hyperparameters}

Given resource constraints, each model is tested a single time on the benchmark. To mitigate response variance, we configure the decoding temperature to 0 for text generation. The maximum output length is also limited to 2048 tokens to maintain consistency and comparability across all experiments.

\paragraph{Prompt setting}
We also applied CoT prompting to encourage structured, step-by-step reasoning. In our evaluation, we employed a Zero-Shot CoT~\cite{Zero-Shot-COT} prompting strategy, which enhances model reasoning by incorporating explicit reasoning cues, such as ``Let's think step by step''. 

% Given that our evaluation focuses on reasoning-intensive System 2 tasks,Since we are more concerned about the reasoning ability of the model, 



\subsection{Main Results}
% those with the highest overall scores in each model scale
% QwQ-32B-Preview and Qwen2.5-32B-Instruct
\paragraph{Overall Performance} Due to space limitations, we only select the representative models, such as GPT-4o, the top-scoring 7/14/32/70/72B models, and several LLMs with over 100B parameters, as illustrated in Table \ref{tab:benchmark_results}. The remaining results can be found in the Appendix \ref{appendix:moreResult}. Based on the results, we have the following observations. First, Deepseek-R1 consistently surpasses a wide array of open-source and closed-source models, including OpenAI's GPT-4o, across all evaluated subjects, demonstrating exceptional strengths in reasoning, knowledge retrieval, and multilingual adaptability, making it highly competitive. Notably,Qwen3-32B with explicit reasoning achieve or even surpass the performance of 70B-sized models. Furthermore, for System2 questions in \anesbench, most models score below 0.5. This suggests that current LLMs still face significant challenges in anesthesiology reasoning and attests to the difficulty and quality of our newly collected questions.

\subsection{Multi-Dimensional Analysis}

\begin{figure}[htbp] %
    \begin{subfigure}[b]{0.49\textwidth}
        \includegraphics[width=\textwidth]{figs/updated1_models_en.pdf}
         % \includegraphics[width=\textwidth]{figs/system_scores_en.pdf}
        \caption{}
        \label{fig:system_scores_en}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.49\textwidth}
        % \includegraphics[width=\textwidth]{figs/system_scores_cn.pdf}
         \includegraphics[width=\textwidth]{figs/updated1_models_cn.pdf}
        \caption{}
        \label{fig:system_scores_cn}
    \end{subfigure}
    \caption{\textbf{Performance Impact of Model Scale.} Colors denote model scale (model index) and problem type (scatter points). Models are sorted by overall score in ascending order. (a) Evaluation Results on \anesbench. (b) Evaluation Results on AMCQA.}
    \label{fig:two}
\end{figure}


\paragraph{Performance Across Model Scale} We arrange the models in increasing order according to their total scores on \anesbench and AMCQA. Fig. \ref{fig:two} reveal a strong positive correlation between model performance and model scale, and this correlation exhibits diminishing marginal returns. Each unit increase in model scale brings progressively smaller performance gains. Fig. \ref{fig:system_scores_en} shows that the fitted-line slopes for System1 and System1.x on \anesbench are nearly identical. In contrast, the slope for System2 is significantly lower. This indicates that performance gains from increasing model size are notably lower for System2 compared to System1. Fig. \ref{fig:system_scores_cn} further shows that the slopes differences for different problem types on AMCQA are less pronounced. These results indicate that our \anesbench exhibits well-defined and distinct difficulty levels.

\begin{wrapfigure}{l}{0.5\textwidth}
  \centering
  \vspace{-10pt}
  \includegraphics[width=0.45\textwidth]{figs/translation_lollipop.pdf}
  % \vspace{-10pt}
  \caption{\textbf{Multilingual Assessment on Translated Benchmarks.} Background colors denote base models: purple for Qwen2.5-7B, blue for Llama-3.1-8B. Here, ``Q'' and ``L'' respectively refer to models trained based on Qwen and Llama.}
  \label{fig:translation}
\end{wrapfigure}

\paragraph{Performance Across Language}
We evaluate multiple models on \anesbench and AMCQA, along with their English-Chinese translations, to investigate multilingual performance in anesthesiology. As shown in Figure~\ref{fig:translation}, Qwen2.5-7B-based and other models exhibit minimal disparity between English and Chinese performance. In contrast, Llama-3.1-8B-based models, despite claiming multilingual support~\cite{Llama3}, perform notably worse in Chinese. These results indicate that language transferability remains a key factor influencing multilingual model performance. According to the theory proposed in~\cite{rise}, once an LLM reaches a stable language learning stage, different languages form independent knowledge systems rather than relying on translation. At this stage, a lack of domain-specific knowledge within the language-specific knowledge system can lead to substantial performance disparities across languages. Therefore, we recommend supplementing bilingual domain-specific knowledge during the CPT stage to mitigate cross-linguistic performance gaps.



% \begin{figure}[ht]
%   \centering
%   \includegraphics[width=0.65\textwidth]{figs/translation_lollipop.pdf}
%   \caption{\textbf{Multilingual Assessment on Translated Benchmarks.} Background colors denote base models: purple for Qwen2.5-7B, blue for Llama-3.1-8B. Here, ``Q'' and ``L'' respectively refer to models trained based on Qwen and Llama.}
% \label{fig:translation}
% \end{figure}

% \paragraph{Performance Across Language} % 多语言-简单一带
% We evaluate multiple models on \anesbench and AMCQA, along with their English-Chinese translations, to investigate multilingual performance in anesthesiology. As shown in Fig. \ref{fig:translation}, Qwen2.5-7B based and other models exhibit minimal disparity between English and Chinese performance. In contrast, Llama-3.1-8B-based models, despite claiming multilingual support~\cite{Llama3}, perform notably worse in Chinese than in English. These results indicate that language transferability remains a key factor influencing multilingual model performance. According to the theory proposed in~\cite{rise}, once an LLM reaches a stable language learning stage, different languages form independent knowledge systems rather than relying on translation. At this stage, a lack of domain-specific knowledge within the language-specific knowledge system can lead to substantial performance disparities across languages. Therefore, we recommend supplementing bilingual domain-specific knowledge during the CPT stage to mitigate cross-linguistic performance gaps.


%\footnote{For a fair and effective comparison, we only selected models obtained by fine-tuning on widely used base models.}
\paragraph{Performance Across Output Length} 
By comparing output length and scores across three types of questions in \anesbench, we discovered: (1) Models with longer CoT reasoning processes tend to exhibit superior response performance. As illustrated in the right subplot of Fig. \ref{fig:lengthVSscore}, for System2 type questions, models achieve higher scores with longer outputs. (2) However, this trend is not pronounced in System1 and System1.x, which do not require extensive reasoning processes. As shown in the left and middle subplots of Fig. \ref{fig:lengthVSscore}, the scores of models are almost solely correlated with the size of the model.


\begin{figure*}[ht]
    \centering
    \includegraphics[width=1\textwidth]{figs/models_length.pdf}
    \caption{\textbf{Output length (log scale) vs. scores.} Shapes 
 and colors denote model families and scales.}
    \label{fig:lengthVSscore}
\end{figure*}
% \begin{figure*}[ht]
%     \centering
%         \begin{subfigure}{0.325\textwidth}
%         \includegraphics[width=\textwidth]{figs/system_1.pdf}
%         \subcaption{}
%         \label{fig:system_1}
%     \end{subfigure}
%     \begin{subfigure}{0.325\textwidth}
%         \includegraphics[width=\textwidth]{figs/system_1.x.pdf}
%         \subcaption{}
%         \label{fig:system_1.x}
%     \end{subfigure}
%     \begin{subfigure}{0.325\textwidth}
%         \includegraphics[width=\textwidth]{figs/system_2.pdf}
%         \subcaption{}
%         \label{fig:system_2}
%     \end{subfigure}
%     \caption{\textbf{Output length (log scale) vs. scores.} Shapes 
%  and colors denote model families and scales, respectively.}
%     \label{fig:lengthVSscore}
% \end{figure*}


\section{Impact of Training \& Reasoning Strategy}
\subsection{Experiments Setup}
\paragraph{CPT Settings} We fine-tune Qwen2.5-7B-Base on the anesthesia-related corpus to obtain Qwen2.5-7B-Base-CPT. The training is conducted for 2 epochs, using a batch size of 8 with gradient accumulation steps of 8. Training is performed in BF16 precision to optimize memory efficiency. 

\paragraph{SFT Settings} After CPT ,we conduct comparative experiments using Qwen2.5-7B-Base and Qwen2.5-7B-Base-CPT. Specifically, we perform an ablation study by fine-tuning these two models on Medical-o1~\cite{huatuogpt} and AnesQA separately and in combination. We use a batch size of 16 with gradient accumulation steps of 4, and the learning rate is 1.0e-5. To ensure a fair comparison across datasets of varying sizes, we fix the total training steps at 2,000 for all experiments.

\paragraph{Reasoning Settings} For reasoning evaluation, we experiment with Best-of-N~\cite{yao2023tree} sampling and Beam Search~\cite{beamSearch} to assess different reasoning strategies. The decoding parameters are set to temperature = 1.0 and top-p = 0.9 to balance diversity and determinism. Additionally, key parameters such as mini-step token budget, tree width, and beam width are systematically varied as experimental factors to analyze their impact on reasoning performance.

\subsection{Effectiveness of Training Strategies}
We conduct experiments on Qwen2.5-7B-Base and Qwen2.5-7B-Base-CPT models, fine-tuning them using AnesQA and Medical-o1 separately and in combination, and then evaluate on \anesbench. Additionally, we use the Qwen2.5-7B-Instruct as a reference model. The results are demonstrated in Table~\ref{tab:training}, providing two key insights into the effectiveness of these training strategies.

\begin{table*}[ht]
    \centering
    \caption{\textbf{Effectiveness of Training Strategies.} The \textbf{Qwen2.5-7B-Base-CPT} model is trained on our custom anesthesiology-related CPT corpus, with \textbf{Qwen2.5-7B-Base} serving as the foundation model.}
    \setlength\extrarowheight{0pt}
    \renewcommand{\arraystretch}{0.45}
    \setlength{\tabcolsep}{10pt}
    % \renewcommand{\arraystretch}{1.2}
    \begin{tabular}{c|cc|c}
        \toprule
        \multirow{2}{*}{Model} & \multicolumn{2}{c|}{SFT Data} & Accuracy \\  
        \cline{2-4}
        & AnesQA & Medical-o1 & \anesbench \\  
        
        \midrule
        \textbf{Qwen2.5-7B-Instruct} & - & - & 51.5 \\ 
        \midrule
        \multirow{3}{*}{\textbf{Qwen2.5-7B-Base}}     
            & \tikzcmark & \tikzxmark & 49.3\\  
            & \tikzxmark & \tikzcmark & 49.1\\  
            & \tikzcmark & \tikzcmark & 49.7\\  
        \midrule
        \multirow{3}{*}{\textbf{Qwen2.5-7B-Base-CPT}} 
            & \tikzcmark & \tikzxmark & 49.7\\  
            & \tikzxmark & \tikzcmark & 50.7\\  
            & \tikzcmark & \tikzcmark & 51.2\\  
        \bottomrule
    \end{tabular}
    \vspace{7pt}
    \label{tab:training}
\end{table*}

\paragraph{Impact of Continuous Pre-Training.} Our results reveal that CPT significantly improves model performance on the \anesbench, with the Qwen2.5-7B-Base-CPT model outperforming its non-CPT counterpart across all SFT data configurations. This domain-adaptive pre-training approach demonstrates clear effectiveness, achieving a benchmark accuracy of up to 51.2 compared to the non-CPT version's maximum of 49.7. 
Notably, using the full SFT dataset (AnesQA and Medical-o1) yields the greatest performance boost for the CPT model, bringing it close to Qwen2.5-7B-Instruct, indicating that combining domain-adaptive pre-training with comprehensive supervised fine-tuning data produces optimal results.


\paragraph{Complementarity of AnesQA and Medical-o1.} The results further highlight the complementary nature of AnesQA and Medical-o1 for fine-tuning. Neither dataset alone is sufficient to optimize model performance across all benchmarks, but combining them yields the best results. Specifically, AnesQA improves the model's problem-solving ability, while Medical-o1 provides critical domain-specific knowledge. This combination emphasizes that both domain knowledge and problem-solving capability are essential for effective model reasoning performance.

\subsection{Impact of Reasoning Techniques}
To assess the impact of different reasoning strategies on model performance, we use the best SFT model, which is Qwen2.5-7B-Base-CPT trained on both AnesQA and medical-o1, and then compare Best-of-N and Beam Search using varying tree widths (number of candidate paths), beam sizes, and step token budgets. The results, presented in Table \ref{tab:reasoning_strategies}, reveal notable trends.

Firstly, in Best-of-N sampling, increasing tree width from 4 to 8 does not yield additional gains. This suggests that simply expanding the candidates without increasing the search space provides limited benefits. In contrast, Beam Search consistently outperforms Best-of-N, demonstrating that step-level structured exploration is more beneficial to reasoning ability. Furthermore, increasing the beam size leads to incremental improvements in accuracy. 

Overall, from a test-time computation perspective, increasing the exploration path width requires higher computational resources but results in a performance boost. By allocating more computation during test time, the model can explore a broader range of paths, leading to more comprehensive reasoning and, ultimately, better outcomes.




\begin{table*}[h]
    \centering
    \setlength\extrarowheight{0pt}
    \renewcommand{\arraystretch}{0.45}
    \setlength{\tabcolsep}{4pt}
    % \renewcommand{\arraystretch}{1.1}
    \caption{\textbf{Comparison of Different Reasoning Strategies.}}
    \label{tab:reasoning_strategies}
    \vspace{5pt}
    \begin{tabular}{c|ccc|c}
        \toprule
        \textbf{Reasoning Strategy} & \textbf{Tree Width} & \textbf{Beam Size} & \textbf{Step Tokens} & \textbf{Accuracy(\%)} \\  
        \midrule
        \textbf{None} 
            & -  & - & - & 51.2 \\  
        \midrule
        \multirow{2}{*}{\textbf{Best-of-N}~\cite{yao2023tree}}  
            & 4  & - & - & 51.5 \\  
            & 8  & - & - & 51.5 \\  
        \midrule
        \multirow{2}{*}{\textbf{Beam Search}~\cite{beamSearch}} 
            & 4  & 4 & 32   & 52.1 \\  
            & 8  & 8 & 32   & \textbf{52.4} \\  
        \bottomrule
    \end{tabular}
\end{table*}


\subsection{Evaluation of Distillation Performance}


\begin{wrapfigure}{r}{0.45\textwidth}
    \centering
    \vspace{-13pt} % 可根据排版微调图片位置
    \includegraphics[width=0.45\textwidth]{figs/impact_of_r1_distillation.pdf}
    % \vspace{-10pt} % 控制图片下方空白
    \caption{\textbf{Impact of R1 distillation.} ``It'' in the figure abbreviates ``Instruct''.}
    \label{impact_of_r1_distillation}
\end{wrapfigure}

As shown in Figure~\ref{impact_of_r1_distillation}, our comparative analysis of DeepSeek-R1 distilled models and instruction-tuned models
% \footnote{We exclude Qwen2.5-Math-1.5B and Qwen2.5-Math-7B as these base models lack adaptability to non-mathematical domains, which may cause an unfair comparison.}
derived from the same base model reveals two key observations: \textbf{(1)} Reasoning capabilities developed in general domains can enhance model performance on anesthesiology-related reasoning tasks. Specifically, models initialized from Qwen2.5-32B-Base and Llama-3.3-70B-Instruct and subsequently distilled using R1 outperformed their instruction-tuned counterparts in both System 2 and System 1.x type questions. \textbf{(2)} However, the effectiveness of R1 distillation correlates positively with model size. As noted in~\cite{SmallModelStuggleLearnReasoning}, small LLMs struggle to acquire reasoning abilities from long CoT data due to their lack of domain-specific knowledge, compared to larger LLMs in this regard. Notably, as model size increases from left to right in the results, the performance improvement from R1 distillation becomes more pronounced for System 2 questions requiring explicit reasoning.


% \begin{figure}[h]
%     \centering
%         \includegraphics[width=0.65\textwidth]{figs/impact_of_r1_distillation.pdf}
%     \caption{\textbf{Impact of R1 distillation.} ``It'' in the figure abbreviates ``Instruct''.}
%     \label{impact_of_r1_distillation}
% \end{figure}


% As shown in Figure \ref{impact_of_r1_distillation}, our comparative analysis of DeepSeek-R1 distilled models and instruction-tuned models\footnote{We exclude Qwen2.5-Math-1.5B and Qwen2.5-Math-7B as these base models lack adaptability to non-mathematical domains, which may cause an unfair comparison.} derived from the same base model reveals two key observations: \textbf{(1)}  Reasoning capabilities developed in general domains can enhance model performance on anesthesiology-related reasoning tasks. Specifically, models initialized from Qwen2.5-32B-Base and Llama-3.3-70B-Instruct and subsequently distilled using R1 outperformed their instruction-tuned counterparts in both System 2 and System 1.x type questions. \textbf{(2)} However, the effectiveness of R1 distillation correlates positively with model size. As noted in~\cite{SmallModelStuggleLearnReasoning}, small LLMs struggle to acquire reasoning abilities from long CoT data due to their lack of domain-specific knowledge, compared to larger LLMs in this regard. Notably, as model size increases from left to right in the results, the performance improvement from R1 distillation becomes more pronounced for System 2 questions requiring explicit reasoning.

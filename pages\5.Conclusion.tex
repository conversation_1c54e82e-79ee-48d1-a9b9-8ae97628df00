\section{Conclusion}

In this work, we introduce \anesbench, an anesthesiology evaluation benchmark featuring a three-level cognitive demand system. Through a comprehensive analysis of evaluation results on over thirty state-of-the-art LLMs, we derive key insights into the relationship between model characteristics and anesthesiology reasoning ability. Additionally, extensive experiments and analyses demonstrate the effectiveness of training strategies such as CPT and SFT, as well as various reasoning techniques. We hope that \anesbench and our multidimensional analysis serve as a effective stepping stone for developing LLMs with enhanced anesthesiology reasoning capabilities.

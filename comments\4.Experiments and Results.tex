\section{Impact of Model Characteristics}

In this section, we conduct a comprehensive evaluation of state-of-the-art LLMs. Through rigorous analysis of the results, we derived several key insights. These insights contribute to a deeper understanding of the potential and limitations of LLMs in this domain.
% model size, reasoning ability, language performance, and knowledge distillation on performance in anesthesiology-specific scenarios. 

\begin{table*}[t]
\caption{\textbf{Main Evaluation Results on AnesBench and AMCQA:} The highest and second-highest scores in the ``Overall'' column are highlighted in bold and underlined, respectively. Only a selection of representative models is shown here; full evaluation results are in Table~\ref{tab:moreResult_0} in Appendix~\ref{appendix:moreResult}.}
\setlength\extrarowheight{3.5pt}
\renewcommand{\arraystretch}{0.85}
\setlength{\tabcolsep}{2pt}
% \vskip 0.15in
\resizebox{\textwidth}{!}{
\begin{tabular}{lp{1.5mm}rrrrp{1.5mm}rrrrp{1.5mm}c}
\toprule
\multirow{2}{*}{Model} && \multicolumn{4}{c}{\textbf{\anesbench}} && \multicolumn{4}{c}{\textbf{AMCQA}} && \multirow{2}{*}{\textbf{Overall}} \\
\cline{3-6} \cline{8-11}
 && Sys1 & Sys1.x & Sys2 & Total && Sys1 & Sys1.x & Sys2 & Total && \\
 \midrule
 \multicolumn{13}{c}{\textbf{\textasciitilde7B models}} \\
 \midrule
ChatGLM3-6B                   &  & 0.37   & 0.28   & 0.25   & 0.34         &  & 0.36  & 0.36  & 0.34  & 0.36        &  & 0.35    \\
HuatuoGPT-o1-7B               &  & 0.56   & 0.45   & 0.38   & 0.52         &  & 0.71  & 0.65  & 0.63  & 0.70        &  & 0.63    \\
Internlm3-8b-instruct         &  & 0.60   & 0.43   & 0.40   & 0.54         &  & 0.85  & 0.76  & 0.77  & 0.84        &  & 0.73    \\
Glm-4-9b-chat                 &  & 0.48   & 0.36   & 0.36   & 0.44         &  & 0.61  & 0.60  & 0.56  & 0.61        &  & 0.55    \\
 \midrule
 \multicolumn{13}{c}{\textbf{> 10B models}} \\
 \midrule
Baichuan2-13B-Chat            &  & 0.42   & 0.31   & 0.34   & 0.39         &  & 0.48  & 0.47  & 0.46  & 0.48        &  & 0.45    \\
Qwen2.5-14B-Instruct          &  & 0.61   & 0.52   & 0.41   & 0.57         &  & 0.74  & 0.70  & 0.62  & 0.73        &  & 0.67    \\
Gemma-2-27b-it                &  & 0.60   & 0.43   & 0.36   & 0.54         &  & 0.57  & 0.52  & 0.48  & 0.56        &  & 0.55    \\
Qwen2.5-32B-Instruct          &  & 0.65   & 0.55   & 0.44   & 0.61         &  & 0.77  & 0.73  & 0.69  & 0.76        &  & 0.70    \\
QwQ-32B-Preview               &  & 0.69   & 0.58   & 0.44   & 0.64         &  & 0.74  & 0.70  & 0.68  & 0.73        &  & 0.70    \\
Llama-3-70B-UltraMedical      &  & 0.73   & 0.60   & 0.47   & 0.68         &  & 0.72  & 0.68  & 0.62  & 0.71        &  & 0.70    \\
Qwen2.5-72B-Instruct          &  & 0.72   & 0.60   & 0.48   & 0.67         &  & 0.82  & 0.77  & 0.76  & 0.81        &  & 0.76    \\
HuatuoGPT-o1-72B              &  & 0.71   & 0.61   & 0.48   & 0.67         &  & 0.82  & 0.78  & 0.78  & 0.81        &  & 0.76    \\
 \midrule
 \multicolumn{13}{c}{\textbf{DeepSeek V3/R1 \& GPT-4o}} \\
 \midrule
DeepSeek-V3                   &  & 0.77   & 0.69   & 0.55   & 0.73         &  & 0.79  & 0.77  & 0.70  & 0.78        &  &  \underline{0.77}    \\
DeepSeek-R1                   &  & 0.85   & 0.78   & 0.70   & 0.82         &  & 0.88  & 0.85  & 0.81  & 0.87        &  & \textbf{0.85}    \\
GPT-4o                        &  & 0.81   & 0.72   & 0.59   & 0.77         &  & 0.78  & 0.77  & 0.68  & 0.78        &  & \underline{0.77}    \\
\bottomrule
\end{tabular}
}
\label{tab:benchmark_results}
\end{table*}

\subsection{Evaluation Setup}

% 在Prompt Strategy上，由于我们所选取的比较模型都为Chat或者Instruct模型，我们认为这些模型都有基本的指令跟随能力，我们选取了

% 为了便于从模型的响应中提取正确答案选项，我们采用了零样本提示提示来指导大型语言模型仅生成正确的选项。

% 为了评估模型处理系统 2 推理任务的能力，我们利用了思维链（CoT）提示，以便更深入地考察它们的推理能力。

% In our evaluation, we opted for a zero-shot prompting approach to measure the models' intrinsic performance without the influence of few-shot examples, which could potentially skew the results. 
% \paragraph{Prompt Strategy}
% In our evaluation, we chose a zero-shot prompting strategy to eliminate potential biases that could arise from example sets in few-shot prompting. Furthermore, despite selecting chat or instruction-tuned models, we refrained from enforcing output format restrictions, acknowledging the variability in instruction-following capabilities across models. To assess the models' performance on System 2 reasoning tasks, we utilized COT prompting, enabling a deeper analysis of their reasoning capabilities.

\paragraph{Decoding Hyperparameters}

Given resource constraints, each model is tested a single time on the benchmark. To mitigate response variance, we configure the decoding temperature to 0 for text generation. The maximum output length is also limited to 2048 tokens to maintain consistency and comparability across all experiments.




\subsection{Main Results}


% 筛选部分留在main result，全表放到appendix





% \begin{table}[htbp]
% \centering
% \caption{\textbf{Main (partial) Evaluation Results on AnesBench and AMCQA:} Top scores per column are highlighted in light orange, runner-ups in turquoise. \includegraphics[width=0.16in]{figs/med.png} indicates medical/biological domain training; \includegraphics[width=0.16in]{figs/think.png} denotes explicit chain-of-thought integration.}
% \label{tab:benchmark_results}
% \begin{tabular}{l|cccc|cccc}
% \toprule
% \multirow{2}{*}{Model} & \multicolumn{4}{c|}{\textbf{AnesBench EN}} & \multicolumn{4}{c}{\textbf{AMCQA}} \\
% \cmidrule(lr){2-5} \cmidrule(lr){6-9}
%  & Sys1 & Sys1.x & Sys2 & All & Sys1 & Sys1.x & Sys2 & All \\
% \midrule
% \multicolumn{9}{c}{\textbf{\textasciitilde7B models}} \\

% \bottomrule
% \end{tabular}
% \end{table}

% \begin{table}[htbp]
% \centering
% \caption{\textbf{Main (partial) Evaluation Results on AnesBench and AMCQA:} Top scores per column are highlighted in light orange, runner-ups in turquoise. \includegraphics[width=0.16in]{figs/med.png} indicates medical/biological domain training; \includegraphics[width=0.16in]{figs/think.png} denotes explicit chain-of-thought integration.}
% \label{tab:benchmark_results}
% \begin{tabular}{l|cccc|cccc}
% \toprule
% \multirow{2}{*}{Model} & \multicolumn{4}{c|}{\textbf{AnesBench EN}} & \multicolumn{4}{c}{\textbf{AMCQA}} \\
% \cmidrule(lr){2-5} \cmidrule(lr){6-9}
%  & Sys1 & Sys1.x & Sys2 & All & Sys1 & Sys1.x & Sys2 & All \\
% \midrule
% \multicolumn{9}{c}{\textbf{\textasciitilde7B models}} \\
% \midrule  
% % \includegraphics[width=0.16in]{figs/med.png}
% ChatGLM3-6B                                                           & 0.36 & 0.27 & 0.25 & 0.33 & 0.36 & 0.36 & 0.34 & 0.36 \\
% Qwen2.5-7B-Instruct                                                   & 0.56 & 0.44 & 0.35 & 0.51 & 0.68 & 0.63 & 0.58 & 0.67 \\
% \includegraphics[width=0.16in]{figs/med.png}BioMistral-7B             & 0.42 & 0.30 & 0.32 & 0.39 & 0.26 & 0.24 & 0.27 & 0.25 \\
% % Meta-Llama-3-8B-Instruct                                              & 0.53 & 0.41 & 0.38 & 0.49 & 0.49 & 0.47 & 0.46 & 0.48 \\
% Llama-3.1-8B-Instruct                                                 & 0.58 & 0.45 & 0.36 & 0.53 & 0.52 & 0.53 & 0.55 & 0.52 \\
% \includegraphics[width=0.16in]{figs/med.png}Llama-3.1-8B-UltraMedical & 0.62 & 0.47 & 0.40 & 0.57 & 0.54 & 0.51 & 0.49 & 0.52 \\
% \includegraphics[width=0.16in]{figs/med.png}\includegraphics[width=0.16in]{figs/think.png}HuatuoGPT-o1-8B           & 0.57 & 0.45 & 0.38 & 0.53 & 0.56 & 0.53 & 0.56 & 0.56 \\
% \includegraphics[width=0.16in]{figs/med.png}Llama3-OpenBioLLM-8B      & 0.44 & 0.34 & 0.30 & 0.40 & 0.25 & 0.24 & 0.19 & 0.25 \\
% \includegraphics[width=0.16in]{figs/med.png}Bio-Medical-Llama-3-8B    & 0.53 & 0.40 & 0.38 & 0.48 & 0.47 & 0.46 & 0.49 & 0.47 \\
% \midrule
% \multicolumn{9}{c}{\textbf{> 10B models}} \\
% \midrule 
% \includegraphics[width=0.16in]{figs/med.png}Baichuan2-13B-Chat     & 0.42 & 0.30 & 0.34 & 0.39 & 0.48 & 0.47 & 0.46 & 0.47 \\
% Phi-4                                                              & 0.68 & 0.57 & 0.41 & 0.63 & 0.56 & 0.57 & 0.55 & 0.56 \\
% \includegraphics[width=0.16in]{figs/think.png} QwQ-32B-Preview                                                    & 0.68 & 0.58 & 0.43 & 0.64 & 0.73 & 0.69 & 0.68 & 0.72 \\
% Yi-1.5-34B-Chat                                                    & 0.54 & 0.43 & 0.34 & 0.50 & 0.65 & 0.63 & 0.64 & 0.64 \\
% \includegraphics[width=0.16in]{figs/med.png}\includegraphics[width=0.16in]{figs/think.png}HuatuoGPT-o1-72B       & 0.71 & 0.60 & 0.48 & 0.66 & \cellcolor{second}0.82 & \cellcolor{second}0.77 & \cellcolor{second}0.78 & \cellcolor{second}0.81 \\
% \includegraphics[width=0.16in]{figs/med.png}Llama3-OpenBioLLM-70B  & 0.68 & 0.55 & 0.43 & 0.62 & 0.65 & 0.60 & 0.60 & 0.64 \\
% \includegraphics[width=0.16in]{figs/med.png}UltraMedical-70B       & 0.72 & 0.60 & 0.46 & 0.67 & 0.72 & 0.67 & 0.61 & 0.71 \\
% Qwen2.5-72B-Instruct                                               & 0.71 & 0.59 & 0.48 & 0.67 & \cellcolor{second}0.82 & \cellcolor{second}0.77 & 0.76 & \cellcolor{second}0.81 \\
% \includegraphics[width=0.16in]{figs/med.png}\includegraphics[width=0.16in]{figs/think.png}Citrus1.0-llama-70B    & 0.70 & 0.60 & 0.52 & 0.66 & 0.70 & 0.69 & 0.67 & 0.70 \\
% Llama-3.3-70B-Instruct                                             & 0.73 & 0.63 & 0.51 & 0.69 & 0.69 & 0.65 & 0.62 & 0.68 \\
% \midrule
% \multicolumn{9}{c}{\textbf{Deepseek family}} \\
% \midrule 
% DeepSeek-V3                                                        & 0.77 & 0.69 & 0.54 & 0.73 & 0.78 & \cellcolor{second}0.77 & 0.69 & 0.78 \\
% \includegraphics[width=0.16in]{figs/think.png}DeepSeek-R1                                                        & \cellcolor{highest}0.84 & \cellcolor{highest}0.77 & \cellcolor{highest}0.69 & \cellcolor{highest}0.81 & \cellcolor{highest}0.88 & \cellcolor{highest}0.85 & \cellcolor{highest}0.81 & \cellcolor{highest}0.87 \\
% \midrule
% \multicolumn{9}{c}{\textbf{GPT}} \\
% \midrule 
% GPT-4o                                                             & \cellcolor{second}0.80 & \cellcolor{second}0.71 & \cellcolor{second}0.59 & \cellcolor{second}0.76 & 0.78 & 0.76 & 0.68 & 0.77 \\
% \bottomrule
% \end{tabular}
% \end{table}

% 根据在表\ref{}选择的部分有代表性的实验结果(全部的实验结果和测试的模型可以在Appendiex \ref{}中查阅),如下的现象可以被观察到

% Based on the experimental results in the Table \ref{tab:benchmark_results}, t

% 由于篇幅限制，我们仅选取了每个model size中overall分数最高的放在Table \ref{}，例如分别6B,7B,8B模型中分数最高的模型. 剩余的结果可以在附录\ref{}中查阅。其中每一列中全局最高分数和次最高分数使用了不同颜色进行标注。

% For example, the models with the highest scores among the 6B, 7B, and 8B models respectively.

\paragraph{Overall Performance} Due to space limitations, we only select the most representative models, such as those with the highest overall scores in each model scale, as illustrated in Table \ref{tab:benchmark_results}. The remaining results can be found in the Appendix \ref{appendix:moreResult}. Based on the results, we have the following observations. First, Deepseek-R1 consistently surpasses a wide array of open-source and closed-source models, including OpenAI's GPT-4o, across all evaluated subjects, demonstrating exceptional strengths in reasoning, knowledge retrieval, and multilingual adaptability, making it highly competitive. Notably, QwQ-32B-Preview and Qwen2.5-32B-Instruct achieve or even surpass the performance of 70B-sized models. Furthermore, for System2 questions in \anesbench, most models score below 0.5. This suggests that current LLMs still face significant challenges in anesthesiology reasoning and attests to the difficulty and quality of our newly collected questions.

\subsection{Multi-Dimensional Analysis}

\begin{figure}[htbp] %
    \begin{subfigure}[b]{0.49\textwidth}
        \includegraphics[width=\textwidth]{figs/system_scores_en.pdf}
        \caption{}
        \label{fig:system_scores_en}
    \end{subfigure}
    \hfill
    % \begin{subfigure}[b]{0.45\textwidth}
    %     \includegraphics[width=\textwidth]{figs/translation_lollipop.pdf}
    %     \caption{}
    %     \label{fig:translation}
    % \end{subfigure}
    % (b) \textbf{Multilingual Assessment on Translated Benchmarks.} Background colors denote base models: purple for Qwen2.5-7B, blue for Llama-3.1-8B.
    \begin{subfigure}[b]{0.49\textwidth}
        \includegraphics[width=\textwidth]{figs/system_scores_cn.pdf}
        \caption{}
        \label{fig:system_scores_cn}
    \end{subfigure}
    \caption{\textbf{Performance Impact of Model Scale.} Colors denote model scale (model index) and problem type (scatter points). Models are sorted by overall score in ascending order. (a) Evaluation Results on \anesbench. (b) Evaluation Results on AMCQA.}
    \label{fig:two}
\end{figure}




% 不同范式问题斜率不同

% 首先，图\ref{} (a) 中最直观的结论为，在麻醉学语境下，无论问题类型，模型表现都与模型size呈现显著的正相关关系，且尚不存在边际效应递减的情况。具体在不同类型问题上，不同类型问题受model size的影响不同。可以发现，system1和system1.x拟合直线的斜率基本相同。但是system2类型问题的拟合直线斜率显著低于前者。这同时表明了我们Benchmark问题难度上的区分度，更表明仅通过增加model size的方式，system2类型问题上的收益是要低于system1的。

% Moreover, a highly intuitive finding is that, in the context of anesthesiology, the performance of models across various types of questions exhibits a significant positive correlation with model size. 


% 我们将不同模型根据在\textbf{AnesBench EN}和\textbf{AMCQA}上overall的分数从总向右递增排列。从Figure \ref{fig:system_scores_en}和Figure \ref{fig:system_scores_cn}可以发现，模型表现与模型的model scale呈现了强烈的正相关关系。但是这个关系呈现了边际效用递减的过程，也就是模型增大单位大小的scale带来的性能提升逐渐减少。此外，从Figure \ref{fig:system_scores_en}可以发现，对于\textbf{AnesBench EN}the slopes of the fitted lines for System1 and System1.x are nearly identical. However, the slope of the fitted line for System2 is significantly lower than that of the former. This indicates that the performance gains from increasing model size are notably lower for System2 compared to System1. In contrast, as shown in Figure \ref{fig:system_scores_cn}, while the slopes of the fitted line for different problem types in the \textbf{AMCQA} also show some variation, the differences are far less pronounced. This further highlights that our newly collected \textbf{AnesBench EN} features a well-structured difficulty progression.

% The most intuitive conclusion from Figure \ref{fig:system_scores_en} is that, in the context of anesthesiology, model performance exhibits a significant positive correlation with no apparent diminishing marginal effects observed so far. Specifically, the impact of model size varies across different problem types. It can be observed that the slopes of the fitted lines for System1 and System1.x are nearly identical. However, the slope of the fitted line for System2 is significantly lower than that of the former. This not only demonstrates the differentiation in difficulty levels within our AnesBench but also indicates that the performance gains from increasing model size are notably lower for System2 compared to System1. In contrast, as shown in Figure \ref{fig:system_scores_cn}, while the slopes of the fitted line for different problem types in the \textbf{AMCQA} also show some variation, the differences are far less pronounced. This further highlights that our newly collected \textbf{AnesBench EN} features a well-structured difficulty progression.

\paragraph{Performance Across Model Scale} We arrange the models in increasing order according to their total scores on \anesbench and AMCQA. Fig. \ref{fig:two} reveal a strong positive correlation between model performance and model scale, and this correlation exhibits diminishing marginal returns. Each unit increase in model scale brings progressively smaller performance gains. Fig. \ref{fig:system_scores_en} shows that the fitted-line slopes for System1 and System1.x on \anesbench are nearly identical. In contrast, the slope for System2 is significantly lower. This indicates that performance gains from increasing model size are notably lower for System2 compared to System1. Fig. \ref{fig:system_scores_cn} further shows that the slopes differences for different problem types on AMCQA are less pronounced. These results indicate that our \anesbench exhibits well-defined and distinct difficulty levels.


% This result shows that our \anesbench is organized with a clear, step-by-step increase in difficulty.

% This finding underscores that our newly collected \anesbench features a well-structured difficulty progression.


% 同时，我们想探究多语言对于麻醉性能的影响
% 目前已经有研究表明在Chat-GPT进行知识获取型任务测评时，使用英文的准确率显著高于日语和中文。我们在这里选取了数个模型，分别在AnesBench和AMCQA上及其中英文翻译版本上进行测试，探究不同模型在麻醉学上的多语言表现。如 图 \ref{} 所示，基于Qwen的和其他的数个模型并没有在中英文能力上显示出较大的差异。而基于Llama-3.1-8B的模型（虽然声称支持多语言），但是中文测试上的表现要显著低于使用英文进行测试。这可能与Llama与仍然强烈依赖于其英语能力，而中文与英文使用了截然不同的orthographies进而导致的语言转换时的信息损耗有关。

% Prior studies have shown that ChatGPT achieves significantly higher accuracy in English than in Japanese or Chinese for knowledge access task.

% 而英文与中文使用不同的orthographies。 

% \begin{figure}[h]
%     \centering
%     \begin{subfigure}[b]{0.5\textwidth}
%         \includegraphics[width=1\textwidth]{figs/translation_lollipop.pdf}
%         \caption{}
%         \label{translation_lollipop}
%     \end{subfigure}
%     \hfill
%     \begin{subfigure}[b]{0.48\textwidth}
%         \includegraphics[width=1\textwidth]{figs/impact_of_r1_distillation.pdf}
%         \caption{}
%         \label{impact_of_r1_distillation}
%     \end{subfigure}
%     \caption{(a) \textbf{Multilingual Assessment on Translated Benchmarks.} Background colors denote base models: purple for Qwen2.5-7B, blue for Llama-3.1-8B. (b) \textbf{Impact of R1 distillation} 'It' in the figure abbreviates 'Instruct'.}
% \end{figure}





% \begin{wrapfigure}{r}{0.4\textwidth}
%   \centering
%   \includegraphics[width=0.4\textwidth]{figs/translation_lollipop.pdf}
%   \caption{\textbf{Multilingual Assessment on Translated Benchmarks.} Background colors denote base models: purple for Qwen2.5-7B, blue for Llama-3.1-8B.}
% \label{translation_lollipop}
% \end{wrapfigure}

% \begin{figure}[h]
%     \centering
%         \includegraphics[width=0.5\textwidth]{figs/translation_lollipop.pdf}
%     \caption{(a) \textbf{Multilingual Assessment on Translated Benchmarks.} Background colors denote base models: purple for Qwen2.5-7B, blue for Llama-3.1-8B.}
%     \label{translation_lollipop}
% \end{figure}

\begin{figure}[h]
  \centering
  \includegraphics[width=0.65\textwidth]{figs/translation_lollipop.pdf}
  \caption{\textbf{Multilingual Assessment on Translated Benchmarks.} Background colors denote base models: purple for Qwen2.5-7B, blue for Llama-3.1-8B. Here, ``Q'' and ``L'' respectively refer to models trained based on Qwen and Llama.}
\label{fig:translation}
\end{figure}

% 其中，``Q''和``L''分别代指基于Qwen和Llama训练出的模型。Here, “Q” and “L” respectively refer to models trained based on Qwen and Llama.

\paragraph{Performance Across Language} % 多语言-简单一带
We evaluate multiple models on \anesbench and AMCQA, along with their English-Chinese translations, to investigate multilingual performance in anesthesiology. As shown in Fig. \ref{fig:translation}, Qwen2.5-7B based and other models exhibit minimal disparity between English and Chinese performance. In contrast, Llama-3.1-8B-based models, despite claiming multilingual support~\cite{Llama3}, perform notably worse in Chinese than in English. These results indicate that language transferability remains a key factor influencing multilingual model performance. According to the theory proposed in~\cite{rise}, once an LLM reaches a stable language learning stage, different languages form independent knowledge systems rather than relying on translation. At this stage, a lack of domain-specific knowledge within the language-specific knowledge system can lead to substantial performance disparities across languages. Therefore, we recommend supplementing bilingual domain-specific knowledge during the CPT stage to mitigate cross-linguistic performance gaps.




 % 这表明，Language transferability仍然是影响multilingual models表现的重要因素。根据~\cite{rise}的理论，LLM的语言学习进入稳定阶段后不同语言会形成独立的知识体系而不再依赖于翻译。如果此时，语言独立知识体系中的domain specific知识不足可能就会产生巨大的语言性能差异。 因此，我们建议在CPT阶段补充双语的领域专业知识,以缓解语言间性能差异。
 
 
 % This may be due to Llama's reliance on its strong English proficiency. Moreover, the distinct writing systems of English and Chinese can result in information loss during translation~\cite{ChatGPTDifferentLanguageDifferentPerforamce,multilingualism}, as languages from different linguistic families have unique characters and rules that do not always have direct equivalents. 
 
 
 
 % This could be attributed to Llama's dependence on its English proficiency. However, the distinct writing systems of English and Chinese can lead to information loss during translation~\cite{ChatGPTDifferentLanguageDifferentPerforamce,multilingualism}. Languages from different linguistic families have unique characters and rules that may not directly correspond to one another.
 
 
 % However, the orthographic differences between English and Chinese, leading to information loss during language transfer~\cite{ChatGPTDifferentLanguageDifferentPerforamce,multilingualism}. 

 % 因此，domain specific




% 我们想探究模型本身对于麻醉推理问题性能的影响（而非基于verifier和RL等搜索范式的影响，因为所有的开源模型测试均采用了贪婪并且温度为0的解码策略）。转而，我们使用对数输出长度作为单纯模型思考缜密程度的替代指标。当然这种代替不完全严谨，我们更希望通过这样的分析可以为开发推理能力更强的LLM提供Insight。

% 近年来，通过在提示中添加 “Let's think step by step” 等特定关键词，Zero-shot CoT 方法因其在无需示例情况下激发模型推理能力而受到关注。基于此思路，我们在Benchmark实验中应用了这一方法，并通过统计模型的输出长度来量化其推理表现。进一步地，我制作了一张图，展示了输出长度与得分之间的正相关关系，即输出越长，得分越高，从而为模型推理能力的评估提供了有力的实证依据。

% While this substitution is not entirely rigorous, we hope this analysis can offer valuable insights for developing LLMs with enhanced reasoning capabilities.

% 我们选取了四个最常用的

% 近年来，通过在提示中添加 “Let's think step by step” 等特定关键词，Zero-shot CoT 方法因其在无需示例情况下激发模型推理能力而受到关注。 目前仅探究了使用COT相较于不使用COT，模型倾向于产生更长的输出和更高的响应准确率。但是，在模型之间横向比较，是否倾向于拥有更长COT推理过程的模型总会有更高的分数呢？此外，上述问题是否在不同类型问题上的结论是相同的呢？ 为此，为了消除可能的偏差，我们选取了数个在最常用的基础模型上训练得到的衍生模型。通过在AnesBench的三种不同类型问题子集上做输出长度与分数的对比图，我们发现：（1） 拥有更长COT推理过程的模型获得更高的响应性能。 如图 \ref{1} 右侧子图所示，在system2类型问题上，基于四种基础模型训练得到的模型输出长度更长的往往有更高的分数。 （2）但是，这种现象在不需要长推理过程的System1和System1.x上并不显著。如图 \ref{1} 左侧和中间子图所示，模型的分数几乎只与模型的大小有关。


% We aim to explore the impact of the model's inherent inference time scaling on performance in anesthesia reasoning tasks, excluding the influence of search paradigms such as verifier-based or RL-based methods, as all open-source models were tested using greedy decoding with a temperature of 0. Instead, we employ the logarithm of output length as a proxy for the model's reasoning thoroughness. 

% 为了公平和有效地对比，我们仅选取了在广泛使用的base model（包括Qwen2.5-7B,Llama-3.1-8B,Qwen2.5-72B和Llama-3.1-70B）上微调的得到的模型。For a fair and effective comparison, we only selected models obtained by fine-tuning on widely used base models (including Qwen2.5-7B, Llama-3.1-8B, Qwen2.5-72B, and Llama-3.1-70B).

% 

\paragraph{Performance Across Output Length} 
By comparing output length and scores across three types of questions in \anesbench\footnote{For a fair and effective comparison, we only selected models obtained by fine-tuning on widely used base models (including Qwen2.5-7B, Llama-3.1-8B, Qwen2.5-72B, and Llama-3.1-70B).}, we discovered: (1) Models with longer CoT reasoning processes tend to exhibit superior response performance. As illustrated in the right subplot of Fig. \ref{fig:lengthVSscore}, for System2 type questions, models achieve higher scores with longer outputs. (2) However, this trend is not pronounced in System1 and System1.x, which do not require extensive reasoning processes. As shown in the left and middle subplots of Fig. \ref{fig:lengthVSscore}, the scores of models are almost solely correlated with the size of the model.

\begin{figure*}[h]
    \centering
        \begin{subfigure}{0.325\textwidth}
        \includegraphics[width=\textwidth]{figs/system_1.pdf}
        \subcaption{}
        \label{fig:system_1}
    \end{subfigure}
    \begin{subfigure}{0.325\textwidth}
        \includegraphics[width=\textwidth]{figs/system_1.x.pdf}
        \subcaption{}
        \label{fig:system_1.x}
    \end{subfigure}
    \begin{subfigure}{0.325\textwidth}
        \includegraphics[width=\textwidth]{figs/system_2.pdf}
        \subcaption{}
        \label{fig:system_2}
    \end{subfigure}
    \caption{\textbf{Output length (log scale) vs. scores.} Shapes 
 and colors denote model families and scales, respectively.}
    \label{fig:lengthVSscore}
\end{figure*}


% \begin{figure}[h]
%     \centering
%     \includegraphics[width=0.7\textwidth]{figs/impact_of_r1_distillation.pdf}
%     \caption{\textbf{Impact of R1 distillation} Excluded models: Qwen2.5-Math-1.5B and Qwen2.5-Math-7B. These base models lack adaptability to non-mathematical domains. 'It' in the figure abbreviates 'Instruct'.}
%     \label{fig:ImpactOfR1}
% \end{figure}




% 基于R1进行蒸馏得到的一系列模型仅通过监督微调就在数学与代码等需要强推理的任务上取得了突出的效果。然而这种成功是否在与通用推理问题具有显著域差异的麻醉学问题上结论相一致呢？ 如图\ref{}, 我们对比了相同base model的DS R1蒸馏后的模型和对应的指令微调模型，并得到两点观察：（1） 通用域上推理的推理能力可以提升模型在麻醉学推理问题上的能力。如在Qwen.25-32B和Llama-3.3-70B-Instruct上使用R1进行蒸馏的模型均比对应的指令微调模型在system2和system1.x分数有所提高。 （2） 但是，越小的模型R1蒸馏效果越差，这可能与小LLM相较于大LLM更难从COT数据中学习到推理能力有关。可以看到，从左向右模型越大，R1蒸馏对于System2即需要推理的题目提升效果越明显。

% 虽然，基于R1进行蒸馏得到的一系列模型仅通过监督微调就在数学与代码等需要强推理的任务上取得了突出的效果。而在需要大量知识获取的麻醉推理问题上，balabala，效果很好但是没有像类似于代码和数学问题一样突出的表现。此外，在小模型上蒸馏的效果较为一般，排除掉由于qwen-math本身基础模型的问题。以上两点实验现象都证明，除了良好的通用推理能力，解决麻醉相关的问题，更需要在CPT和SFT阶段提供更多的麻醉相关知识。 此外，更大的模型

% 可能可以说？大部分知识都是在预训练阶段获得的，但是SFT阶段的数据更多关注于推理相关内容，由于较小的模型容量使得模型产生了灾难性遗忘
% 参数量较小的模型（3B 参数）并不能始终从长链式思考 (CoT) 推理或从更大模型的蒸馏中获益。


% However, its performance on the Chinese AMCQA, while commendable, is less pronounced compared to its results on the English AnesBench. This discrepancy is likely due to the insufficient representation of Chinese data in the multilingual corpus used during pre-training.

% \paragraph{Overall Performance} Based on the partial experimental results in the table \ref{tab:benchmark_results}, the following conclusions can be drawn: (1) Deepseek-R1 consistently surpasses a wide array of open-source and closed-source models, including OpenAI's GPT-4o, across all evaluated subjects. This achievement underscores its strengths in reasoning, knowledge retrieval, and multilingual adaptability, making it highly competitive. (2) It is noteworthy that Phi-4, with only 14B parameters, matches or outperforms ~30B models on AnesBench. On the Chinese AMCQA, its performance is commendable but not as prominent as its results on the English AnesBench. This may be attributed to the insufficient amount of Chinese data in the multilingual corpus used during pre-training.



% (1) Current LLMs still struggle with reasoning problems (System 2). For instance, GPT-4 achieves only 0.59 on AnesBench for such problems, while most models score below 0.4, demonstrating the benchmark's difficulty and its effectiveness in evaluating reasoning in anesthesiology. 



% (2) Long COT reasoning training yields better performance on reasoning problems. For example, QWQ-32B, similar in size to Yi-34B, shows a significant improvement in reasoning scores. Similarly, HuatuoGPT-o1 outperforms UltraMedical (post-trained on the same base model) on AMCQA. Additionally, Deepseek-R1 achieves the highest scores on both benchmarks. (3) In the context of anesthesiology, post-training on medical data does not always outperform general-domain data. For instance, UltraMedical and Llama3.3 are both derived from Llama3.1, yet the latter achieves higher scores on AnesBench.




% \paragraph{}% 两种检测方法及结果

\section{Impact of Training \& Reasoning Strategy}
\subsection{Experiments Setup}
\paragraph{CPT Settings} We fine-tune Qwen2.5-7B-Base on the anesthesia-related corpus to obtain Qwen2.5-7B-Base-CPT. The training is conducted for 2 epochs, using a batch size of 8 with gradient accumulation steps of 8. Training is performed in BF16 precision to optimize memory efficiency. 

\paragraph{SFT Settings} After CPT ,we conduct comparative experiments using Qwen2.5-7B-Base and Qwen2.5-7B-Base-CPT. Specifically, we perform an ablation study by fine-tuning these two models on Medical-o1~\cite{huatuogpt} and AnesQA separately and in combination. We use a batch size of 16 with gradient accumulation steps of 4, and the learning rate is 1.0e-5. To ensure a fair comparison across datasets of varying sizes, we fix the total training steps at 2,000 for all experiments.

\paragraph{Reasoning Settings} For reasoning evaluation, we experiment with Best-of-N~\cite{yao2023tree} sampling and Beam Search~\cite{beamSearch} to assess different reasoning strategies. The decoding parameters are set to temperature = 1.0 and top-p = 0.9 to balance diversity and determinism. Additionally, key parameters such as mini-step token budget, tree width, and beam width are systematically varied as experimental factors to analyze their impact on reasoning performance.

\subsection{Effectiveness of Training Strategies}
We conduct experiments on Qwen2.5-7B-Base and Qwen2.5-7B-Base-CPT models, fine-tuning them using AnesQA and Medical-o1 separately and in combination, and then evaluate on \anesbench (English) and AMCQA (Chinese). Additionally, we use the Qwen2.5-7B-Instruct as a reference model. The results are demonstrated in Table~\ref{tab:training}, providing two key insights into the effectiveness of these training strategies.

\begin{table*}[h]
    \centering
    \caption{\textbf{Effectiveness of Training Strategies.} The \textbf{Qwen2.5-7B-Base-CPT} model is trained on our custom anesthesiology-related CPT corpus, with \textbf{Qwen2.5-7B-Base} serving as the foundation model.}
    \setlength{\tabcolsep}{6pt}
    \renewcommand{\arraystretch}{1.2}
    % \caption{Performance of different models on medical benchmarks}
    
    \begin{tabular}{c|cc|cc}
        \toprule
        \multirow{2}{*}{Model} & \multicolumn{2}{c|}{SFT Data} & \multicolumn{2}{c}{Benchmark Accuracy} \\  
        \cline{2-5}
        & AnesQA & medical-o1 & AnesBench (EN) & AMCQA (CN) \\  
        
        \midrule
        \textbf{Qwen2.5-7B-Instruct} & - & - & 51.5 & 67.3 \\ 
        \midrule
        \multirow{3}{*}{\textbf{Qwen2.5-7B-Base}}     
            & \tikzcmark & \tikzxmark & 49.3 & 62.6 \\  
            & \tikzxmark & \tikzcmark & 49.1 & 63.6 \\  
            & \tikzcmark & \tikzcmark & 49.7 & 65.0 \\  
        \midrule
        \multirow{3}{*}{\textbf{Qwen2.5-7B-Base-CPT}} 
            & \tikzcmark & \tikzxmark & 49.7 & 57.6 \\  
            & \tikzxmark & \tikzcmark & 50.7 & 58.9 \\  
            & \tikzcmark & \tikzcmark & 51.2 & 60.1 \\  
        \bottomrule
    \end{tabular}
    \vspace{7pt}
    \label{tab:training}
\end{table*}

\paragraph{Impact of Continuous Pre-Training.} Our results reveal that CPT significantly improves model performance on the English benchmark (\anesbench). The Qwen2.5-7B-Base-CPT model outperforms its non-CPT counterpart across all settings in \anesbench, demonstrating the effectiveness of domain-adaptive pre-training. However, CPT leads to a performance drop on the Chinese benchmark (AMCQA), suggesting that it may not effectively boost Chinese-language reasoning. This could be attributed to Qwen's strong inherent capabilities in Chinese, making additional pre-training less beneficial or even slightly detrimental.

%Notably, when the full SFT dataset (AnesQA and medical-o1) is utilized, the CPT model shows the most significant performance improvement, nearly matching the Qwen2.5-7B-Instruct model's level, indicating that combining domain-adaptive pre-training with comprehensive supervised fine-tuning data produces optimal results.

\paragraph{Complementarity of AnesQA and Medical-o1.} The results further highlight the complementary nature of AnesQA and Medical-o1 for fine-tuning. Neither dataset alone is sufficient to optimize model performance across all benchmarks, but combining them yields the best results. Specifically, AnesQA improves the model's problem-solving ability, while Medical-o1 provides critical domain-specific knowledge. This combination emphasizes that both domain knowledge and problem-solving capability are essential for effective model reasoning performance.

\subsection{Impact of Reasoning Techniques}
To assess the impact of different reasoning strategies on model performance, we use the best SFT model, which is Qwen2.5-7B-Base-CPT trained on both AnesQA and medical-o1, and then compare Best-of-N and Beam Search using varying tree widths (number of candidate paths), beam sizes, and step token budgets. The results, presented in Table \ref{tab:reasoning_strategies}, reveal notable trends.

Firstly, in Best-of-N sampling, increasing tree width from 4 to 8 does not yield additional gains. This suggests that simply expanding the candidates without increasing the search space provides limited benefits. In contrast, Beam Search consistently outperforms Best-of-N, demonstrating that step-level structured exploration is more beneficial to reasoning ability. Furthermore, increasing the beam size leads to incremental improvements in accuracy. 

Overall, from a test-time computation perspective, increasing the exploration path width requires higher computational resources but results in a performance boost. By allocating more computation during test time, the model can explore a broader range of paths, leading to more comprehensive reasoning and, ultimately, better outcomes.

% Notably, expanding both tree width and beam size in Beam Search yields the highest accuracy, albeit at the cost of increased test-time computation. This indicates that the additional reasoning depth and structured hypothesis refinement contribute meaningfully to improved performance.

% Overrall, from a test-time compute perspective, although Beam Search introduces additional computational overhead compared to Best of N, its performance gains justify the trade-off, particularly for applications where reasoning accuracy is paramount. 



\begin{table*}[h]
    \centering
    \setlength{\tabcolsep}{4pt}
    \renewcommand{\arraystretch}{1.1}
    \caption{\textbf{Comparison of Different Reasoning Strategies.}}
    \label{tab:reasoning_strategies}
    \vspace{5pt}
    \begin{tabular}{c|ccc|c}
        \toprule
        \textbf{Reasoning Strategy} & \textbf{Tree Width} & \textbf{Beam Size} & \textbf{Step Tokens} & \textbf{Accuracy(\%)} \\  
        \midrule
        \textbf{None} 
            & -  & - & - & 51.2 \\  
        \midrule
        \multirow{2}{*}{\textbf{Best-of-N}}  
            & 4  & - & - & 51.5 \\  
            & 8  & - & - & 51.5 \\  
        \midrule
        \multirow{2}{*}{\textbf{Beam Search}} 
            & 4  & 4 & 32   & 52.1 \\  
            & 8  & 8 & 32   & \textbf{52.4} \\  
        \bottomrule
    \end{tabular}
\end{table*}


\subsection{Evaluation of Distillation Performance}

\begin{figure}[h]
    \centering
        \includegraphics[width=0.6\textwidth]{figs/impact_of_r1_distillation.pdf}
    \caption{\textbf{Impact of R1 distillation.} ``It'' in the figure abbreviates ``Instruct''.}
    \label{impact_of_r1_distillation}
\end{figure}

% A series of models distilled from R1 have demonstrated remarkable performance on tasks requiring strong reasoning, such as mathematics and coding, through supervised fine-tuning alone. However, does this success remain consistent in anesthesiology-related problems that exhibit significant domain shifts from general reasoning tasks? 

As shown in Figure \ref{impact_of_r1_distillation}, our comparative analysis of DeepSeek-R1 distilled models and instruction-tuned models\footnote{We exclude Qwen2.5-Math-1.5B and Qwen2.5-Math-7B as these base models lack adaptability to non-mathematical domains, which may cause an unfair comparison.} derived from the same base model reveals two key observations: \textbf{(1)}  Reasoning capabilities developed in general domains can enhance model performance on anesthesiology-related reasoning tasks. Specifically, models initialized from Qwen2.5-32B-Base and Llama-3.3-70B-Instruct and subsequently distilled using R1 outperformed their instruction-tuned counterparts in both System 2 and System 1.x type questions. \textbf{(2)} However, the effectiveness of R1 distillation correlates positively with model size. As noted in~\cite{SmallModelStuggleLearnReasoning}, small LLMs struggle to acquire reasoning abilities from long CoT data due to their lack of domain-specific knowledge, compared to larger LLMs in this regard. Notably, as model size increases from left to right in the results, the performance improvement from R1 distillation becomes more pronounced for System 2 questions requiring explicit reasoning.

% This phenomenon may stem from smaller LLMs' reduced capacity to learn reasoning abilities from COT data compared to larger LLMs~\cite{SmallModelStuggleLearnReasoning}.

% 如研究~\cite{SmallModelStuggleLearnReasoning}所提到的，这种现象可能与小LLM缺乏领域知识进而相较于更大的LLM难以从长COT数据中学习到推理能力。

% As noted in~\cite{SmallModelStuggleLearnReasoning}, small LLMs may struggle to acquire reasoning abilities from long CoT data due to their lack of domain-specific knowledge, compared to larger LLMs in this regard.
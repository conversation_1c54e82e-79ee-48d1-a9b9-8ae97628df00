\section{Conclusion}

In this work, we introduce \anesbench, an anesthesiology evaluation benchmark featuring a three-level cognitive demand system. Through a comprehensive analysis of evaluation results on over thirty state-of-the-art LLMs, we derive key insights into the relationship between model characteristics and anesthesiology reasoning ability. Additionally, extensive experiments and analyses demonstrate the effectiveness of training strategies such as CPT and SFT, as well as various reasoning techniques. We hope that \anesbench and our multidimensional analysis serve as a effective stepping stone for developing LLMs with enhanced anesthesiology reasoning capabilities.

% 在这个工作中，我们提出了\anesbench，一个具有三级认知需求分级系统的麻醉学评估benchmark。 通过我们在三十多个最先进LLMs上evaluation结果的分析， 我们得到了多个model characteristic与麻醉学推理能力关系的insights。此外，通过广泛的实验和分析，我们证明了包括CPT和SFT在内训练策略以及多个reasoning techniques的有效性。 我们希望我们的\anesbench和多维度的分析成为开发具有更强麻醉学推理能力LLM的有力垫脚石。

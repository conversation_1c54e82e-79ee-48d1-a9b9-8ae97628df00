\begin{figure}[ht]
\setlength{\belowcaptionskip}{-10pt}
  \centering
  \includegraphics[width=0.98\textwidth]{figs/overview.pdf}
  \vspace{-5pt}
  \caption{\textbf{Overview of our work and proposed benchmark} The top section of the figure presents the benchmark’s basic information, including examples for three question types and statistics. The bottom section displays multiple dimensions that may be related to the model’s reasoning ability, as revealed by our analyses and experiments.}
\label{fig:overview}
\end{figure}

\section{Introduction}
\label{sec:intro}


The success of LLMs has fundamentally reshaped the domain of medical artificial intelligence and catalyzed the creation of specialized medical models~\cite{LLMSuccess}. However, the reasoning abilities of LLMs within the medical sector still need significant improvement, particularly when compared with general reasoning tasks such as mathematics and programming~\cite{medicalLagsBehind}.

Anesthesiology, as a highly specialized discipline, requires extensive medical knowledge and high-risk decision-making. General medical Question-Answering (QA) primarily involves fact retrieval (System 1), whereas anesthesia decision-making demands complex, context-aware judgments and reasoning ability (System 2)~\cite{Fast&SlowThinking}. Existing benchmarks overlook unique characteristics of anesthesiology or limit to general fact retrieval~\cite{AnesBenchmark_explicit_1,AnesBenchmark_explicit_2,CMB,AnesBenchmark_implicit_1,AnesBenchmark_implicit_2,AnesBenchmark_implicit_3,AnesBenchmark_implicit_4,CAB}. Our study aims to systematically assess LLMs' anesthesia reasoning capabilities and investigate the factors that influence reasoning abilities.

To this end, we construct \anesbench, a comprehensive cross-lingual benchmark designed to evaluate both fact retrieval and decision-making in anesthesia-related tasks. \anesbench categorizes reasoning tasks into three levels. System 1 focuses on fact retrieval. System 2 involves complex reasoning and decision-making. System 1.x represents a hybrid category that requires both factual recall and basic reasoning~\cite{System1.x}. Utilizing \anesbench, we conduct multiple experiments and carry out a detailed and multi-dimensional analysis of the anesthesiology reasoning capabilities of representative LLMs, including those in general and medical domains.


Currently, analyses of factors enhancing LLM reasoning capabilities focus on two main issues. The first issue focuses on identifying the characteristics typically exhibited by LLMs with enhanced reasoning capabilities. The second issue concerns how to endow LLMs with enhanced reasoning skills. In the context of anesthesiology reasoning, our analysis of the first issue considers several fundamental model characteristics, including model scale, CoT~\cite{Zero-Shot-COT} length, and language transferability~\cite{language_trans}. Our analysis of the second issue examines several training and reasoning methodologies, encompassing CPT ~\cite{CPT}, SFT ~\cite{SFT}, knowledge distillation~\cite{knowledge_dis}, and test-time reasoning techniques~\cite{self-consistency,beamSearch,MCTS_1}. 

For the first issue, we analyze reasoning performance across \textbf{51} state-of-the-art LLMs and obtain following key insights: (1) System 1 tasks benefit from larger model scales, while System 2 reasoning exhibits comparatively smaller gains. Notably, both systems experience diminishing marginal returns, with each additional unit of scale contributing progressively less to performance improvements. (2) CoT length plays an important role in enhancing performance, particularly in System 2 tasks, by enabling more structured and step-by-step reasoning. (3) Language transferability is still an important factor limiting the performance of multilingual models. We suggest supplementing bilingual domain-specific data in the CPT stage to alleviate the language gap. 

For the second issue, we investigate how training and reasoning strategies affect anesthesia reasoning. We first construct CPT and SFT datasets for anesthesiology domain and conduct experiments using a two-stage training strategy. Then, we analyze various test-time compute reasoning techniques, including Best-of-N~\cite{yao2023tree} and Beam Search~\cite{beamSearch}. Furthermore, we evaluate LLMs enhanced by DeepSeek-R1 distillation. Our findings indicate that SFT improves System 1 performance but is insufficient to enhance System 2 reasoning. On the other hand, reasoning strategies consistently boost reasoning performance. Additionally, our experiments reveal that larger models benefit more from reasoning distillation than small LLMs when addressing System 2 anesthesia problems. 

Our contribution can be summarized as follows:
\begin{itemize}[itemsep=2pt,topsep=2pt,parsep=0pt]
    \item We introduce a cross-lingual benchmark with a three-level reasoning system (System 1, System 1.x, and System 2) that systematically disentangles knowledge retrieval from reasoning capabilities, bridging a critical gap in anesthesiology evaluation.
    \item Based on extensive evaluation results, our work demonstrates the impact of several model characteristics, such as model scale, language transferability, and COT length, in relation to anesthesiology reasoning performance, yielding several key insights.
    \item Using a curated dataset, our extensive experiments demonstrate the effectiveness of CPT and SFT training strategies while evaluating reasoning techniques such as Best-of-N sampling and beam search. Furthermore, we investigate the impact of distillation on anesthesiology reasoning, using an analysis of DeepSeek-R1 distilled models.
\end{itemize}


Overall, we are committed to leveraging our benchmark and in-depth analyses to provide valuable insights for developing LLMs with enhanced anesthesiology reasoning capabilities. We will publicly release our benchmark, the CPT and SFT datasets, and the evaluation code.
